#!/usr/bin/env python3
"""
Fix Database Filename
This script replaces the corrupted Database.mqh with a clean version that uses the correct filename.
"""

import shutil
from pathlib import Path

# Your MT5 Terminal Path
MT5_TERMINAL_PATH = r"C:\Users\<USER>\AppData\Roaming\MetaQuotes\Terminal\7BC3F33EDFDBDBDBADB45838B9A2D03F\MQL5"

def fix_database_file():
    """Replace the corrupted Database.mqh file with a clean version."""
    
    print("🔧 Fixing Database.mqh file...")
    
    # Source file (our clean version)
    source_file = Path("Database_Fixed.mqh")
    
    # Destination file in terminal
    dest_file = Path(MT5_TERMINAL_PATH) / "Include" / "MultiCurrency" / "Database.mqh"
    
    if not source_file.exists():
        print(f"❌ Source file not found: {source_file}")
        return False
    
    if not dest_file.parent.exists():
        print(f"❌ Destination directory not found: {dest_file.parent}")
        return False
    
    # Backup existing file if it exists
    if dest_file.exists():
        backup_file = dest_file.with_suffix('.mqh.corrupted.backup')
        shutil.copy2(dest_file, backup_file)
        print(f"  📋 Backed up corrupted file to: {backup_file.name}")
    
    # Copy the clean file
    shutil.copy2(source_file, dest_file)
    
    print(f"  ✅ Replaced corrupted Database.mqh with clean version")
    print(f"  📍 Location: {dest_file}")
    print(f"  🔧 Fixed database filename: database.sqlite → database911.sqlite")
    
    return True

def verify_database_file():
    """Verify the Database.mqh file is clean and has correct filename."""
    
    print("\n🔍 Verifying Database.mqh file...")
    
    dest_file = Path(MT5_TERMINAL_PATH) / "Include" / "MultiCurrency" / "Database.mqh"
    
    try:
        with open(dest_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for corruption indicators
        if ' c l a s s ' in content or ' s t r i n g ' in content:
            print("  ❌ File still appears corrupted (spaces between characters)")
            return False
        
        # Check for correct database filename
        if 'database911.sqlite' in content:
            print("  ✅ File content looks clean and has correct database filename")
            return True
        elif 'database.sqlite' in content:
            print("  ⚠️ File has old database filename (database.sqlite)")
            return False
        else:
            print("  ⚠️ File doesn't contain expected database filename")
            return False
            
    except Exception as e:
        print(f"  ❌ Error reading file: {e}")
        return False

def main():
    """Main function."""
    
    print("🔧 Database.mqh Filename Fix")
    print("=" * 50)
    
    # Check if terminal path exists
    if not Path(MT5_TERMINAL_PATH).exists():
        print(f"❌ MT5 Terminal path not found: {MT5_TERMINAL_PATH}")
        return False
    
    # Fix the database file
    if not fix_database_file():
        return False
    
    # Verify the fix
    if not verify_database_file():
        print("\n⚠️ Warning: File verification failed!")
        return False
    
    print("\n🎉 Database.mqh file has been fixed!")
    print("\nNext steps:")
    print("1. Open MetaEditor (F4 in MT5)")
    print("2. Navigate to Include/MultiCurrency/Database.mqh")
    print("3. Open and compile the file to check for errors")
    print("4. Navigate to Experts/Optimization.mq5")
    print("5. Recompile Optimization.mq5")
    print("6. Remove and re-add Optimization.ex5 to EURUSD chart")
    print("7. The system should now find database911.sqlite correctly")
    
    return True

if __name__ == "__main__":
    if main():
        input("\nPress Enter to continue...")
    else:
        input("\nPress Enter to exit...")
