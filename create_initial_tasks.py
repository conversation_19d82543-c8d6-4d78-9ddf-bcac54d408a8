#!/usr/bin/env python3
"""
Create Initial Tasks
This script creates the initial jobs and tasks for the optimization stages.
"""

import sqlite3
from pathlib import Path

# Your MT5 Terminal Path
MT5_TERMINAL_PATH = r"C:\Users\<USER>\AppData\Roaming\MetaQuotes\Terminal\7BC3F33EDFDBDBDBADB45838B9A2D03F\MQL5"

def create_jobs_and_tasks():
    """Create jobs and tasks for the optimization stages."""
    
    print("🔧 Creating jobs and tasks for optimization stages...")
    
    db_path = Path(MT5_TERMINAL_PATH) / "Files" / "database911.sqlite"
    
    if not db_path.exists():
        print(f"❌ Database file not found: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # Get all stages that need jobs and tasks (excluding 'Single tester pass')
        cursor.execute("""
            SELECT id_stage, name, symbol, period, expert 
            FROM stages 
            WHERE name != 'Single tester pass' 
            AND id_stage NOT IN (SELECT DISTINCT id_stage FROM jobs WHERE id_stage IS NOT NULL)
            ORDER BY id_stage;
        """)
        stages = cursor.fetchall()
        
        if not stages:
            print("  ℹ️ All stages already have jobs and tasks")
            return True
        
        print(f"  📋 Creating jobs and tasks for {len(stages)} stages...")
        
        for stage in stages:
            stage_id, stage_name, symbol, period, expert = stage
            
            print(f"    🎯 Stage {stage_id}: {stage_name} - {symbol}")
            
            # Create job for this stage
            cursor.execute("""
                INSERT INTO jobs (id_stage, symbol, period, tester_inputs, status)
                VALUES (?, ?, ?, ?, 'Queued');
            """, (stage_id, symbol, period, "", ))
            
            job_id = cursor.lastrowid
            print(f"      ✅ Created job {job_id}")
            
            # Create task for this job
            cursor.execute("""
                INSERT INTO tasks (id_job, optimization_criterion, status)
                VALUES (?, 7, 'Queued');
            """, (job_id,))
            
            task_id = cursor.lastrowid
            print(f"      ✅ Created task {task_id}")
        
        # Commit all changes
        conn.commit()
        
        # Verify what we created
        cursor.execute("""
            SELECT t.id_task, t.status, s.name as stage_name, s.symbol, p.name as project_name
            FROM tasks t
            JOIN jobs j ON t.id_job = j.id_job
            JOIN stages s ON j.id_stage = s.id_stage
            JOIN projects p ON s.id_project = p.id_project
            WHERE t.status = 'Queued'
            ORDER BY t.id_task;
        """)
        queued_tasks = cursor.fetchall()
        
        print(f"\n✅ Created {len(queued_tasks)} queued tasks:")
        for task in queued_tasks:
            print(f"  Task {task[0]}: {task[4]} - {task[2]} - {task[3]}")
        
        conn.close()
        return True
        
    except sqlite3.Error as e:
        print(f"❌ Database error: {e}")
        return False

def update_stage_experts():
    """Update the expert field in stages to point to correct EA files."""
    
    print("\n🔧 Updating stage expert assignments...")
    
    db_path = Path(MT5_TERMINAL_PATH) / "Files" / "database911.sqlite"
    
    try:
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # Update expert assignments for different stage types
        updates = [
            ("Stage 1 - Individual Optimization", "SimpleVolumesStage1.ex5"),
            ("Stage 2 - Clustering Analysis", "ClusteringStage1.py"),
            ("Stage 3 - Group Optimization", "SimpleVolumesStage2.ex5"),
            ("Stage 4 - Library Export", "SimpleVolumesStage3.ex5")
        ]
        
        for stage_name, expert_file in updates:
            cursor.execute("""
                UPDATE stages 
                SET expert = ? 
                WHERE name = ?;
            """, (expert_file, stage_name))
            
            affected = cursor.rowcount
            if affected > 0:
                print(f"  ✅ Updated {affected} stages: {stage_name} → {expert_file}")
        
        conn.commit()
        conn.close()
        return True
        
    except sqlite3.Error as e:
        print(f"❌ Database error: {e}")
        return False

def show_system_ready():
    """Show that the system is ready to run."""
    
    print("\n🎉 System Ready!")
    print("=" * 50)
    print("✅ Database configured with projects and stages")
    print("✅ Jobs and tasks created for optimization pipeline")
    print("✅ Expert Advisors assigned to appropriate stages")
    print("✅ All files in place and compiled")
    
    print("\n🚀 Next Steps:")
    print("1. Open MetaTrader 5")
    print("2. Open EURUSD chart (or any chart)")
    print("3. Drag Optimization.ex5 onto the chart")
    print("4. Enable all permissions (DLL, WebRequest, etc.)")
    print("5. Watch the Expert tab for system messages")
    
    print("\n📊 Expected Workflow:")
    print("• System will find Task 1: EURUSD Stage 1 optimization")
    print("• It will attempt to start SimpleVolumesStage1.ex5")
    print("• Currently using stub implementation (will log messages)")
    print("• When complete, will move to clustering stage")
    print("• Then continue through all stages automatically")

def main():
    """Main function."""
    
    print("🔧 Multi-Currency FX EA - Task Creator")
    print("=" * 50)
    
    # Update expert assignments
    if not update_stage_experts():
        print("\n❌ Failed to update stage experts!")
        return False
    
    # Create jobs and tasks
    if not create_jobs_and_tasks():
        print("\n❌ Failed to create jobs and tasks!")
        return False
    
    # Show system ready message
    show_system_ready()
    
    return True

if __name__ == "__main__":
    if main():
        input("\nPress Enter to continue...")
    else:
        input("\nPress Enter to exit...")
