#!/usr/bin/env python3
"""
Debug Database Connection
This script checks the database connection and table structure.
"""

import sqlite3
from pathlib import Path

# Your MT5 Terminal Path
MT5_TERMINAL_PATH = r"C:\Users\<USER>\AppData\Roaming\MetaQuotes\Terminal\7BC3F33EDFDBDBDBADB45838B9A2D03F\MQL5"

def check_database_location():
    """Check where the database file should be and if it exists."""
    
    print("🔍 Checking database file location...")
    
    # Check Files directory (where MQL5 usually looks for data files)
    files_db_path = Path(MT5_TERMINAL_PATH) / "Files" / "database911.sqlite"
    
    # Check root MQL5 directory
    root_db_path = Path(MT5_TERMINAL_PATH) / "database911.sqlite"
    
    # Check current working directory
    current_db_path = Path("database911.sqlite")
    
    print(f"📍 Checking locations:")
    print(f"  1. Files directory: {files_db_path}")
    print(f"     Exists: {'✅' if files_db_path.exists() else '❌'}")
    
    print(f"  2. MQL5 root: {root_db_path}")
    print(f"     Exists: {'✅' if root_db_path.exists() else '❌'}")
    
    print(f"  3. Current directory: {current_db_path}")
    print(f"     Exists: {'✅' if current_db_path.exists() else '❌'}")
    
    # Return the path that exists
    if files_db_path.exists():
        return files_db_path
    elif root_db_path.exists():
        return root_db_path
    elif current_db_path.exists():
        return current_db_path
    else:
        return None

def check_database_tables(db_path):
    """Check what tables exist in the database."""
    
    print(f"\n🔍 Checking database tables in: {db_path}")
    
    try:
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # Get list of all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print(f"📋 Found {len(tables)} tables:")
        for table in tables:
            print(f"  ✅ {table[0]}")
        
        # Check if required tables exist
        required_tables = ['tasks', 'jobs', 'stages', 'projects']
        missing_tables = []
        
        table_names = [table[0] for table in tables]
        for required_table in required_tables:
            if required_table not in table_names:
                missing_tables.append(required_table)
        
        if missing_tables:
            print(f"\n❌ Missing required tables: {missing_tables}")
            return False
        else:
            print(f"\n✅ All required tables exist!")
            return True
        
    except sqlite3.Error as e:
        print(f"❌ Database error: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def check_database_content(db_path):
    """Check the content of the database tables."""
    
    print(f"\n🔍 Checking database content...")
    
    try:
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # Check tasks table
        cursor.execute("SELECT COUNT(*) FROM tasks;")
        task_count = cursor.fetchone()[0]
        print(f"📊 Tasks: {task_count}")
        
        # Check jobs table
        cursor.execute("SELECT COUNT(*) FROM jobs;")
        job_count = cursor.fetchone()[0]
        print(f"📊 Jobs: {job_count}")
        
        # Check stages table
        cursor.execute("SELECT COUNT(*) FROM stages;")
        stage_count = cursor.fetchone()[0]
        print(f"📊 Stages: {stage_count}")
        
        # Check projects table
        cursor.execute("SELECT COUNT(*) FROM projects;")
        project_count = cursor.fetchone()[0]
        print(f"📊 Projects: {project_count}")
        
        # Show some sample data
        if task_count > 0:
            print(f"\n📋 Sample tasks:")
            cursor.execute("SELECT id_task, status FROM tasks LIMIT 5;")
            tasks = cursor.fetchall()
            for task in tasks:
                print(f"  Task {task[0]}: {task[1]}")
        
        return True
        
    except sqlite3.Error as e:
        print(f"❌ Database error: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def copy_database_to_files():
    """Copy database to the Files directory if it's not there."""
    
    print(f"\n🔧 Copying database to Files directory...")
    
    # Source database (current directory)
    source_db = Path("database911.sqlite")
    
    # Destination (Files directory)
    dest_db = Path(MT5_TERMINAL_PATH) / "Files" / "database911.sqlite"
    
    if not source_db.exists():
        print(f"❌ Source database not found: {source_db}")
        return False
    
    # Create Files directory if it doesn't exist
    dest_db.parent.mkdir(exist_ok=True)
    
    # Copy the database
    import shutil
    shutil.copy2(source_db, dest_db)
    
    print(f"✅ Database copied to: {dest_db}")
    return True

def main():
    """Main function."""
    
    print("🔍 Database Connection Debug")
    print("=" * 50)
    
    # Check database location
    db_path = check_database_location()
    
    if not db_path:
        print("\n❌ No database file found in any expected location!")
        print("\n🔧 Attempting to copy database to Files directory...")
        if copy_database_to_files():
            db_path = Path(MT5_TERMINAL_PATH) / "Files" / "database911.sqlite"
        else:
            return False
    
    # Check database tables
    if not check_database_tables(db_path):
        print("\n❌ Database structure is incomplete!")
        return False
    
    # Check database content
    if not check_database_content(db_path):
        print("\n❌ Error reading database content!")
        return False
    
    print("\n🎉 Database diagnosis complete!")
    print("\nRecommendations:")
    print("1. Remove Optimization.ex5 from the chart")
    print("2. Drag it back onto the EURUSD chart")
    print("3. The system should now find the database and tasks")
    print("4. Watch for 'Current Task ID = 4' instead of 0")
    
    return True

if __name__ == "__main__":
    if main():
        input("\nPress Enter to continue...")
    else:
        input("\nPress Enter to exit...")
