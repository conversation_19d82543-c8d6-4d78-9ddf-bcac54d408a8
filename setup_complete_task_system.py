#!/usr/bin/env python3
"""
Complete Multi-Currency FX EA Task System Setup
This script ensures ALL necessary tasks, jobs, and stages are properly created
for the complete multi-currency automation system.
"""

import sqlite3
from pathlib import Path
import sys

def check_database_structure():
    """Verify database exists and has correct structure."""
    
    db_path = "Database/database911.sqlite"
    
    if not Path(db_path).exists():
        print(f"❌ Database not found: {db_path}")
        return False, None
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check required tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        required_tables = ['projects', 'stages', 'jobs', 'tasks', 'passes']
        missing_tables = [t for t in required_tables if t not in tables]
        
        if missing_tables:
            print(f"❌ Missing tables: {missing_tables}")
            return False, None
            
        print("✅ Database structure verified")
        return True, conn
        
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False, None

def verify_projects_and_stages(conn):
    """Verify all projects and stages exist."""
    
    cursor = conn.cursor()
    
    # Check projects
    cursor.execute("SELECT id_project, name FROM projects ORDER BY id_project")
    projects = cursor.fetchall()
    
    print(f"\n📋 Found {len(projects)} projects:")
    for proj_id, name in projects:
        print(f"  {proj_id}: {name}")
    
    # Check stages for each project
    cursor.execute("""
        SELECT s.id_stage, s.id_project, s.name, s.symbol, s.expert, s.status
        FROM stages s
        WHERE s.name != 'Single tester pass'
        ORDER BY s.id_project, s.id_stage
    """)
    stages = cursor.fetchall()
    
    print(f"\n📋 Found {len(stages)} optimization stages:")
    for stage_id, proj_id, name, symbol, expert, status in stages:
        print(f"  Stage {stage_id} (Project {proj_id}): {name} - {symbol} - {expert} [{status}]")
    
    return projects, stages

def create_missing_jobs_and_tasks(conn, stages):
    """Create jobs and tasks for all stages that need them."""
    
    cursor = conn.cursor()
    
    print(f"\n🔧 Creating jobs and tasks for {len(stages)} stages...")
    
    created_count = 0
    
    for stage_id, proj_id, stage_name, symbol, expert, status in stages:
        
        # Check if job already exists for this stage
        cursor.execute("SELECT id_job FROM jobs WHERE id_stage = ?", (stage_id,))
        existing_job = cursor.fetchone()
        
        if existing_job:
            job_id = existing_job[0]
            print(f"  ℹ️ Stage {stage_id}: Job {job_id} already exists")
        else:
            # Create job
            cursor.execute("""
                INSERT INTO jobs (id_stage, symbol, period, tester_inputs, status)
                VALUES (?, ?, 'H1', '', 'Queued')
            """, (stage_id, symbol))
            
            job_id = cursor.lastrowid
            print(f"  ✅ Stage {stage_id}: Created job {job_id}")
            created_count += 1
        
        # Check if task already exists for this job
        cursor.execute("SELECT id_task FROM tasks WHERE id_job = ?", (job_id,))
        existing_task = cursor.fetchone()
        
        if existing_task:
            task_id = existing_task[0]
            print(f"    ℹ️ Job {job_id}: Task {task_id} already exists")
        else:
            # Create task
            cursor.execute("""
                INSERT INTO tasks (id_job, optimization_criterion, status)
                VALUES (?, 7, 'Queued')
            """, (job_id,))
            
            task_id = cursor.lastrowid
            print(f"    ✅ Job {job_id}: Created task {task_id}")
            created_count += 1
    
    conn.commit()
    print(f"\n✅ Created {created_count} new jobs/tasks")
    return True

def update_expert_assignments(conn):
    """Ensure all stages have correct expert assignments."""
    
    cursor = conn.cursor()
    
    print(f"\n🔧 Updating expert assignments...")
    
    # Expert assignments for different stage types
    expert_mappings = {
        "Stage 1 - Individual Optimization": "SimpleVolumesStage1",
        "Stage 2 - Clustering Analysis": "ClusteringStage1.py", 
        "Stage 3 - Group Optimization": "SimpleVolumesStage2",
        "Stage 4 - Library Export": "SimpleVolumesStage3"
    }
    
    for stage_name, expert in expert_mappings.items():
        cursor.execute("""
            UPDATE stages 
            SET expert = ? 
            WHERE name = ?
        """, (expert, stage_name))
        
        affected = cursor.rowcount
        if affected > 0:
            print(f"  ✅ Updated {affected} stages: {stage_name} → {expert}")
    
    conn.commit()
    return True

def show_complete_task_overview(conn):
    """Show complete overview of all tasks in the system."""
    
    cursor = conn.cursor()
    
    print(f"\n📊 Complete Task System Overview:")
    print("=" * 80)
    
    # Get all tasks with full details
    cursor.execute("""
        SELECT 
            t.id_task,
            t.status as task_status,
            j.id_job,
            j.status as job_status,
            s.id_stage,
            s.name as stage_name,
            s.symbol,
            s.expert,
            p.name as project_name
        FROM tasks t
        JOIN jobs j ON t.id_job = j.id_job
        JOIN stages s ON j.id_stage = s.id_stage
        JOIN projects p ON s.id_project = p.id_project
        ORDER BY p.id_project, s.id_stage, t.id_task
    """)
    
    tasks = cursor.fetchall()
    
    current_project = None
    for task in tasks:
        task_id, task_status, job_id, job_status, stage_id, stage_name, symbol, expert, project_name = task
        
        if project_name != current_project:
            print(f"\n🎯 {project_name}")
            current_project = project_name
        
        print(f"  Task {task_id}: {stage_name} - {symbol}")
        print(f"    Expert: {expert} | Status: {task_status}")
        print(f"    Job {job_id} | Stage {stage_id}")
    
    print(f"\n📈 Summary:")
    print(f"  Total Tasks: {len(tasks)}")
    
    # Count by status
    cursor.execute("SELECT status, COUNT(*) FROM tasks GROUP BY status")
    status_counts = cursor.fetchall()
    for status, count in status_counts:
        print(f"  {status}: {count} tasks")

def main():
    """Main setup function."""
    
    print("🚀 Multi-Currency FX EA - Complete Task System Setup")
    print("=" * 70)
    
    # Step 1: Check database
    db_ok, conn = check_database_structure()
    if not db_ok:
        return False
    
    try:
        # Step 2: Verify projects and stages
        projects, stages = verify_projects_and_stages(conn)
        
        if not stages:
            print("❌ No optimization stages found!")
            return False
        
        # Step 3: Update expert assignments
        update_expert_assignments(conn)
        
        # Step 4: Create missing jobs and tasks
        create_missing_jobs_and_tasks(conn, stages)
        
        # Step 5: Show complete overview
        show_complete_task_overview(conn)
        
        print(f"\n🎉 Complete Task System Setup Successful!")
        print("=" * 70)
        print("✅ All projects, stages, jobs, and tasks are properly configured")
        print("✅ Expert assignments updated")
        print("✅ System ready for multi-currency automation")
        
        print(f"\n🚀 Next Steps:")
        print("1. Run optimization with Task ID from the overview above")
        print("2. System will automatically process all currency pairs")
        print("3. Each stage will flow to the next automatically")
        print("4. Monitor progress through database or web interface")
        
        return True
        
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    if main():
        print("\n✅ Setup completed successfully!")
        sys.exit(0)
    else:
        print("\n❌ Setup failed!")
        sys.exit(1)
