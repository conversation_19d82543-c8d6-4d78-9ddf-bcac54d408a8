#!/usr/bin/env python3
"""
Fix Corrupted Optimizer.mqh File
This script replaces the corrupted Optimizer.mqh file with a clean version.
"""

import shutil
from pathlib import Path

# Your MT5 Terminal Path
MT5_TERMINAL_PATH = r"C:\Users\<USER>\AppData\Roaming\MetaQuotes\Terminal\7BC3F33EDFDBDBDBADB45838B9A2D03F\MQL5"

def fix_optimizer_file():
    """Replace the corrupted Optimizer.mqh file with a clean version."""
    
    print("🔧 Fixing corrupted Optimizer.mqh file...")
    
    # Source file (our clean version)
    source_file = Path("Optimizer_Fixed.mqh")
    
    # Destination file in terminal
    dest_file = Path(MT5_TERMINAL_PATH) / "Include" / "MultiCurrency" / "Optimizer.mqh"
    
    if not source_file.exists():
        print(f"❌ Source file not found: {source_file}")
        return False
    
    if not dest_file.parent.exists():
        print(f"❌ Destination directory not found: {dest_file.parent}")
        return False
    
    # Backup existing file if it exists
    if dest_file.exists():
        backup_file = dest_file.with_suffix('.mqh.corrupted.backup')
        shutil.copy2(dest_file, backup_file)
        print(f"  📋 Backed up corrupted file to: {backup_file.name}")
    
    # Copy the clean file
    shutil.copy2(source_file, dest_file)
    
    print(f"  ✅ Replaced corrupted Optimizer.mqh with clean version")
    print(f"  📍 Location: {dest_file}")
    
    return True

def verify_file_content():
    """Verify the file content is clean."""
    
    print("\n🔍 Verifying file content...")
    
    dest_file = Path(MT5_TERMINAL_PATH) / "Include" / "MultiCurrency" / "Optimizer.mqh"
    
    try:
        with open(dest_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for corruption indicators
        if ' c l a s s ' in content or ' v o i d ' in content:
            print("  ❌ File still appears corrupted (spaces between characters)")
            return False
        
        if 'class COptimizer' in content and 'void Process()' in content:
            print("  ✅ File content looks clean and correct")
            return True
        else:
            print("  ⚠️ File content doesn't contain expected classes/methods")
            return False
            
    except Exception as e:
        print(f"  ❌ Error reading file: {e}")
        return False

def main():
    """Main function."""
    
    print("🔧 Optimizer.mqh File Corruption Fix")
    print("=" * 50)
    
    # Check if terminal path exists
    if not Path(MT5_TERMINAL_PATH).exists():
        print(f"❌ MT5 Terminal path not found: {MT5_TERMINAL_PATH}")
        return False
    
    # Fix the optimizer file
    if not fix_optimizer_file():
        return False
    
    # Verify the fix
    if not verify_file_content():
        print("\n⚠️ Warning: File verification failed!")
        return False
    
    print("\n🎉 Optimizer.mqh file has been fixed!")
    print("\nNext steps:")
    print("1. Open MetaEditor (F4 in MT5)")
    print("2. Navigate to Include/MultiCurrency/Optimizer.mqh")
    print("3. Open and compile the file to check for errors")
    print("4. Navigate to Experts/Optimization.mq5")
    print("5. Recompile Optimization.mq5")
    print("6. The system should now find Task ID 4 instead of 0")
    
    return True

if __name__ == "__main__":
    if main():
        input("\nPress Enter to continue...")
    else:
        input("\nPress Enter to exit...")
