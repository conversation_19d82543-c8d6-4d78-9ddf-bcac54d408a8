#!/usr/bin/env python3
"""
Sync optimization results from MT5 database to development database
"""
import sqlite3
import shutil
from pathlib import Path

def sync_results():
    """Copy optimization results from MT5 to development database."""
    
    # Database paths
    mt5_db = r"C:\Users\<USER>\AppData\Roaming\MetaQuotes\Terminal\Common\Files\database911.sqlite"
    dev_db = "Database/database911.sqlite"
    
    print("🔄 Syncing Optimization Results")
    print("=" * 50)
    print(f"📥 Source (MT5): {mt5_db}")
    print(f"📤 Target (Dev): {dev_db}")
    
    # Check if MT5 database exists
    if not Path(mt5_db).exists():
        print(f"❌ MT5 database not found: {mt5_db}")
        return False
    
    # Check if dev database exists
    if not Path(dev_db).exists():
        print(f"❌ Development database not found: {dev_db}")
        return False
    
    try:
        # Connect to both databases
        mt5_conn = sqlite3.connect(mt5_db)
        dev_conn = sqlite3.connect(dev_db)
        
        mt5_cursor = mt5_conn.cursor()
        dev_cursor = dev_conn.cursor()
        
        # Check results in MT5 database
        mt5_cursor.execute("SELECT COUNT(*) FROM passes WHERE id_task = 4")
        mt5_count = mt5_cursor.fetchone()[0]
        print(f"📊 MT5 database has {mt5_count} results for Task 4")
        
        # Check current results in dev database
        dev_cursor.execute("SELECT COUNT(*) FROM passes WHERE id_task = 4")
        dev_count = dev_cursor.fetchone()[0]
        print(f"📊 Dev database has {dev_count} results for Task 4")
        
        if mt5_count == 0:
            print("❌ No results to sync from MT5 database")
            return False
        
        if dev_count > 0:
            print("⚠️ Dev database already has results. Clearing old results...")
            dev_cursor.execute("DELETE FROM passes WHERE id_task = 4")
            dev_conn.commit()
        
        # Copy all Task 4 results from MT5 to Dev
        print(f"🔄 Copying {mt5_count} results...")
        
        # Get all Task 4 passes from MT5
        mt5_cursor.execute("SELECT * FROM passes WHERE id_task = 4")
        mt5_results = mt5_cursor.fetchall()
        
        # Get column names for passes table
        mt5_cursor.execute("PRAGMA table_info(passes)")
        columns = [col[1] for col in mt5_cursor.fetchall()]
        
        # Prepare insert statement
        placeholders = ','.join(['?' for _ in columns])
        insert_sql = f"INSERT INTO passes ({','.join(columns)}) VALUES ({placeholders})"
        
        # Insert all results
        dev_cursor.executemany(insert_sql, mt5_results)
        dev_conn.commit()
        
        # Verify the copy
        dev_cursor.execute("SELECT COUNT(*) FROM passes WHERE id_task = 4")
        final_count = dev_cursor.fetchone()[0]
        
        print(f"✅ Successfully copied {final_count} results")
        
        # Update Task 4 status to Done
        dev_cursor.execute("UPDATE tasks SET status = 'Done' WHERE id_task = 4")
        dev_conn.commit()
        print("✅ Updated Task 4 status to 'Done'")
        
        # Show top 5 results (check column names first)
        dev_cursor.execute("PRAGMA table_info(passes)")
        pass_columns = [col[1] for col in dev_cursor.fetchall()]
        print(f"📋 Available columns: {pass_columns}")

        # Use correct column names
        dev_cursor.execute("""
            SELECT id_pass, profit, balance_dd, trades, params
            FROM passes
            WHERE id_task = 4
            ORDER BY profit DESC
            LIMIT 5
        """)
        top_results = dev_cursor.fetchall()

        print(f"\n🏆 Top 5 Results:")
        print("-" * 60)
        for i, (pass_id, profit, balance_dd, trades, params) in enumerate(top_results, 1):
            print(f"{i}. Pass {pass_id}: Profit=${profit:.2f} | DD=${balance_dd:.2f} | Trades={trades}")
            # Extract key parameters
            if 'signalPeriod_=' in params:
                signal_period = params.split('signalPeriod_=')[1].split(',')[0]
                signal_dev = params.split('signalDeviation_=')[1].split(',')[0]
                print(f"   Parameters: SignalPeriod={signal_period}, SignalDeviation={signal_dev}")
        
        mt5_conn.close()
        dev_conn.close()
        
        print(f"\n🎉 Sync completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error during sync: {e}")
        return False

def show_next_steps():
    """Show what to do next."""
    
    print(f"\n🚀 NEXT STEPS:")
    print("=" * 50)
    print("✅ Task 4 (EURUSD Stage 1) completed successfully")
    print("✅ 32,000+ optimization results available")
    print("✅ Ready to proceed to next stages")
    
    print(f"\n📋 Available Next Tasks:")
    print("• Task 5: EURUSD Stage 2 (Clustering Analysis)")
    print("• Task 6: EURUSD Stage 3 (Group Optimization)")  
    print("• Task 7: EURUSD Stage 4 (Library Export)")
    print("• Tasks 8-11: GBPUSD optimization stages")
    print("• Tasks 12-15: EURGBP optimization stages")
    
    print(f"\n🎯 To Continue:")
    print("1. Run the complete task system setup")
    print("2. Start next optimization with Task 5")
    print("3. System will automatically flow through all stages")

if __name__ == "__main__":
    if sync_results():
        show_next_steps()
    else:
        print("❌ Sync failed!")
