#!/usr/bin/env python3
"""
Debug Database Tasks
This script checks what's happening with task selection in the database.
"""

import sqlite3
from pathlib import Path

# Your MT5 Terminal Path
MT5_TERMINAL_PATH = r"C:\Users\<USER>\AppData\Roaming\MetaQuotes\Terminal\7BC3F33EDFDBDBDBADB45838B9A2D03F\MQL5"

def debug_task_selection():
    """Debug the task selection query that COptimizer uses."""
    
    print("🔍 Debugging Task Selection Query")
    print("=" * 50)
    
    db_path = Path(MT5_TERMINAL_PATH) / "Files" / "database911.sqlite"
    
    if not db_path.exists():
        print(f"❌ Database file not found: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # This is the exact query from COptimizer::GetNextTaskId()
        query = """
            SELECT t.id_task 
            FROM tasks t
                JOIN jobs j ON t.id_job = j.id_job
                JOIN stages s ON j.id_stage = s.id_stage
            WHERE t.status IN ('Queued', 'Processing')
            ORDER BY s.id_stage, j.id_job, t.status 
            LIMIT 1;
        """
        
        print("🔍 Running COptimizer query:")
        print(query)
        
        cursor.execute(query)
        result = cursor.fetchone()
        
        if result:
            task_id = result[0]
            print(f"✅ Query should return Task ID: {task_id}")
        else:
            print("❌ Query returns no results!")
        
        # Let's also check what tasks exist
        print("\n📋 All tasks in database:")
        cursor.execute("""
            SELECT t.id_task, t.status, s.id_stage, s.name, s.symbol, j.id_job
            FROM tasks t
            JOIN jobs j ON t.id_job = j.id_job
            JOIN stages s ON j.id_stage = s.id_stage
            ORDER BY t.id_task;
        """)
        
        all_tasks = cursor.fetchall()
        for task in all_tasks:
            print(f"  Task {task[0]}: Status={task[1]}, Stage={task[2]} ({task[3]}), Symbol={task[4]}, Job={task[5]}")
        
        # Check specifically for Queued tasks
        print("\n⏳ Queued tasks only:")
        cursor.execute("""
            SELECT t.id_task, t.status, s.id_stage, s.name, s.symbol, j.id_job
            FROM tasks t
            JOIN jobs j ON t.id_job = j.id_job
            JOIN stages s ON j.id_stage = s.id_stage
            WHERE t.status = 'Queued'
            ORDER BY s.id_stage, j.id_job, t.status;
        """)
        
        queued_tasks = cursor.fetchall()
        for task in queued_tasks:
            print(f"  Task {task[0]}: Status={task[1]}, Stage={task[2]} ({task[3]}), Symbol={task[4]}, Job={task[5]}")
        
        # Check if there are any tasks with IDs 1-3
        print("\n🔍 Checking for tasks with IDs 1-3:")
        cursor.execute("SELECT id_task, status FROM tasks WHERE id_task <= 3 ORDER BY id_task;")
        low_id_tasks = cursor.fetchall()
        
        if low_id_tasks:
            for task in low_id_tasks:
                print(f"  Task {task[0]}: Status={task[1]}")
        else:
            print("  No tasks with IDs 1-3 found")
        
        conn.close()
        return True
        
    except sqlite3.Error as e:
        print(f"❌ Database error: {e}")
        return False

def fix_task_issue():
    """Try to fix the task selection issue."""
    
    print("\n🔧 Attempting to fix task issue...")
    
    db_path = Path(MT5_TERMINAL_PATH) / "Files" / "database911.sqlite"
    
    try:
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # Check if there are any 'Done' tasks with low IDs that might be interfering
        cursor.execute("""
            SELECT id_task, status FROM tasks 
            WHERE id_task <= 3 AND status = 'Done'
            ORDER BY id_task;
        """)
        done_tasks = cursor.fetchall()
        
        if done_tasks:
            print(f"Found {len(done_tasks)} 'Done' tasks with low IDs:")
            for task in done_tasks:
                print(f"  Task {task[0]}: Status={task[1]}")
            
            # These might be from the 'Single tester pass' stages
            # Let's check what they are
            cursor.execute("""
                SELECT t.id_task, t.status, s.name, s.symbol
                FROM tasks t
                JOIN jobs j ON t.id_job = j.id_job
                JOIN stages s ON j.id_stage = s.id_stage
                WHERE t.id_task <= 3
                ORDER BY t.id_task;
            """)
            low_tasks_detail = cursor.fetchall()
            
            print("\nDetails of low ID tasks:")
            for task in low_tasks_detail:
                print(f"  Task {task[0]}: {task[2]} - {task[3]} - Status: {task[1]}")
        
        conn.close()
        return True
        
    except sqlite3.Error as e:
        print(f"❌ Database error: {e}")
        return False

def main():
    """Main function."""
    
    print("🔍 Multi-Currency FX EA - Task Debug Tool")
    print("=" * 60)
    
    if not debug_task_selection():
        return False
    
    if not fix_task_issue():
        return False
    
    print("\n💡 Recommendations:")
    print("1. The system should be finding the lowest ID queued task")
    print("2. If Task ID = 0, it means no tasks are being found by the query")
    print("3. Check if there are encoding issues with the Optimizer.mqh file")
    print("4. The query should return the first queued task (likely Task 4)")
    
    return True

if __name__ == "__main__":
    if main():
        input("\nPress Enter to continue...")
    else:
        input("\nPress Enter to exit...")
