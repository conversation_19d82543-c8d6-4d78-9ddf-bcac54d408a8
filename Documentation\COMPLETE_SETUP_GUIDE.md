# Multi-Currency FX EA Automation System - Complete Setup Guide

## 🎯 Overview

This system provides automated multi-currency optimization with the following workflow:
1. **Stage 1**: Individual strategy optimization (1000s of backtests)
2. **Clustering**: Python-based K-means clustering to select best 256 results
3. **Stage 2**: Group optimization of selected strategies
4. **Stage 3**: Export final strategy groups to library

## 📁 Current File Organization

Your files are now organized as follows:

```
c:\Documents\Dev\Multi-Currency FX EA Automation\
├── MQL5\
│   ├── Experts\           # All .mq5 Expert Advisor files
│   ├── Include\
│   │   └── MultiCurrency\ # All .mqh include files
│   └── Scripts\
├── Python\
│   ├── ClusteringStage1.py
│   └── requirements.txt
├── Database\
│   ├── database911.sqlite      # ✅ Ready to use
│   ├── init_database.sql
│   └── setup_database.py
├── Documentation\
└── Config\
```

## 🔧 Step 1: Copy Files to MetaTrader 5 Terminal

### Find Your MT5 Terminal Directory:
1. Open MetaTrader 5
2. Press `Ctrl+Shift+D` to open Data Folder
3. Navigate to `MQL5` folder

### Create Directory Structure in MT5 Terminal:
```
%APPDATA%\MetaQuotes\Terminal\[YourTerminalID]\MQL5\
├── Experts\
│   └── MultiCurrency\
├── Include\
│   └── MultiCurrency\
└── Files\
```

### Copy Files:

**Expert Advisors** (Copy from `MQL5\Experts\` to MT5 `MQL5\Experts\MultiCurrency\`):
- ✅ `Optimization.mq5` ⭐ (Main controller - compile this first!)
- ✅ `SimpleVolumesExpert.mq5`
- ✅ `SimpleVolumesStage1.mq5`
- ✅ `SimpleVolumesStage2.mq5`
- ✅ `SimpleVolumesStage3.mq5`
- ✅ `HistoryReceiverExpert.mq5`
- ✅ `SimpleHistoryReceiverExpert.mq5`
- ✅ `LibraryExpert.mq5`

**Include Files** (Copy from `MQL5\Include\MultiCurrency\` to MT5 `MQL5\Include\MultiCurrency\`):
- All `.mqh` files (14 files total)

**Database** (Copy to MT5 `MQL5\Files\`):
- ✅ `database911.sqlite` (from `Database\` folder)

**Python Script** (Copy to MT5 `MQL5\Experts\`):
- ✅ `ClusteringStage1.py` (from `Python\` folder)

## 🔨 Step 2: Fix Include Paths for Compilation

Before compiling, you need to update the include paths in the Expert Advisor files.

### Required Changes:

In **ALL** Expert Advisor files (`.mq5` files), change:
```cpp
// FROM:
#include "Database.mqh"
#include "Optimizer.mqh"
// etc...

// TO:
#include "MultiCurrency/Database.mqh"
#include "MultiCurrency/Optimizer.mqh"
// etc...
```

### Files That Need Include Path Updates:
- `Optimization.mq5` ⭐ (Most important)
- `SimpleVolumesExpert.mq5`
- `SimpleVolumesStage1.mq5`
- `SimpleVolumesStage2.mq5`
- `SimpleVolumesStage3.mq5`
- `HistoryReceiverExpert.mq5`
- `SimpleHistoryReceiverExpert.mq5`
- `LibraryExport.mq5`

## ⚙️ Step 3: Compilation Order in MetaEditor

1. **First**: Open MetaEditor in MT5 (F4 or Tools → MetaQuotes Language Editor)

2. **Compile Include Files**: Open and compile all `.mqh` files in `Include\MultiCurrency\`
   - These should compile without errors

3. **Compile Main Controller**: 
   - Open `Experts\MultiCurrency\Optimization.mq5`
   - Fix include paths (add "MultiCurrency/" prefix)
   - Compile - this is the most important file!

4. **Compile Other EAs**: Compile remaining Expert Advisors

## 🚀 Step 4: Initial Testing

### Load the System:
1. In MT5, open any chart (EURUSD recommended)
2. Drag `Optimization.ex5` onto the chart
3. In EA settings, ensure:
   - Allow DLL imports: ✅ Enabled
   - Allow WebRequest: ✅ Enabled
   - Allow external experts: ✅ Enabled

### Check System Status:
1. Open **Expert** tab in Terminal
2. Look for initialization messages
3. Check for database connection confirmation
4. Monitor for task creation messages

## 📊 Step 5: Database Verification

The database is already set up with:
- ✅ 3 Projects (EURUSD, GBPUSD, EURGBP)
- ✅ 8 Optimization stages
- ✅ All required triggers and schema

### Projects Created:
1. **EURUSD Multi-Stage Optimization**
   - Stage 1: Individual Optimization
   - Stage 2: Clustering Analysis  
   - Stage 3: Group Optimization
   - Stage 4: Library Export

2. **GBPUSD Multi-Stage Optimization** (same stages)
3. **EURGBP Multi-Stage Optimization** (ready for future setup)

## 🐍 Step 6: Python Environment

Python packages are installed and ready:
- ✅ pandas (data manipulation)
- ✅ scikit-learn (clustering)
- ✅ numpy (numerical operations)

## 🔍 Troubleshooting

### Common Issues:

1. **Compilation Errors**:
   - Check include paths have "MultiCurrency/" prefix
   - Ensure all .mqh files are in Include\MultiCurrency\

2. **Database Connection Issues**:
   - Verify database911.sqlite is in Files\ folder
   - Check file permissions

3. **Python Script Issues**:
   - Ensure ClusteringStage1.py is in Experts\ folder
   - Verify Python packages are installed

4. **EA Not Starting**:
   - Check DLL imports are enabled
   - Verify Expert Advisors are enabled in MT5

## 📈 Expected Workflow

Once loaded, the system will:
1. **Read database** for queued optimization tasks
2. **Start Stage 1** optimization for EURUSD
3. **Run 1000s of backtests** automatically
4. **Call Python clustering** when Stage 1 completes
5. **Continue to Stage 2** group optimization
6. **Move to next currency pair** when complete

## 🎉 Success Indicators

You'll know it's working when you see:
- ✅ "Database connected successfully" in Expert tab
- ✅ "Starting optimization task..." messages
- ✅ Strategy Tester automatically opening and running
- ✅ Database entries being created and updated
- ✅ Python script being called for clustering

## 📞 Next Steps

After successful setup:
1. Monitor the first optimization run
2. Check database for results
3. Add your custom indicators (Asymmetric Compounding, DEMA-ATR)
4. Scale to additional currency pairs
5. Implement web monitoring dashboard
