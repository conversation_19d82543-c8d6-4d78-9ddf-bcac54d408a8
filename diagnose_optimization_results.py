#!/usr/bin/env python3
"""
Diagnose optimization results and database state
"""
import sqlite3
import os

def diagnose_database():
    db_path = "Database/database911.sqlite"
    
    if not os.path.exists(db_path):
        print(f"❌ Database file {db_path} not found!")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔍 DETAILED DATABASE DIAGNOSIS")
        print("=" * 60)
        
        # Check Task 4 details
        cursor.execute("SELECT * FROM tasks WHERE id_task = 4")
        task4 = cursor.fetchone()
        print(f"📋 Task 4 Details: {task4}")
        
        # Check Job 4 details
        cursor.execute("SELECT * FROM jobs WHERE id_job = 4")
        job4 = cursor.fetchone()
        print(f"📋 Job 4 Details: {job4}")
        
        # Check if there are any passes at all
        cursor.execute("SELECT COUNT(*) FROM passes")
        total_passes = cursor.fetchone()[0]
        print(f"📊 Total passes in database: {total_passes}")
        
        if total_passes > 0:
            cursor.execute("SELECT * FROM passes LIMIT 5")
            sample_passes = cursor.fetchall()
            print(f"📊 Sample passes: {sample_passes}")
        
        # Check all tasks and their relationships
        cursor.execute("""
            SELECT 
                t.id_task, t.status as task_status, t.id_job,
                j.id_job, j.status as job_status, j.id_stage,
                s.id_stage, s.name as stage_name, s.symbol
            FROM tasks t
            LEFT JOIN jobs j ON t.id_job = j.id_job
            LEFT JOIN stages s ON j.id_stage = s.id_stage
            ORDER BY t.id_task
        """)
        
        all_tasks = cursor.fetchall()
        print(f"\n📋 ALL TASKS AND RELATIONSHIPS:")
        for task in all_tasks:
            print(f"  Task {task[0]}: Status={task[1]} | Job={task[2]} | Stage={task[6]} | {task[7]} | {task[8]}")
        
        # Check stages table for EURUSD
        cursor.execute("SELECT * FROM stages WHERE symbol = 'EURUSD'")
        eurusd_stages = cursor.fetchall()
        print(f"\n🎯 EURUSD STAGES:")
        for stage in eurusd_stages:
            print(f"  Stage {stage[0]}: {stage[3]} | Expert: {stage[4]} | Status: {stage[-1]}")
        
        # Check if there are any passes for task 4 specifically
        cursor.execute("""
            SELECT COUNT(*) FROM passes p
            JOIN tasks t ON p.id_task = t.id_task
            WHERE t.id_task = 4
        """)
        task4_passes = cursor.fetchone()[0]
        print(f"\n📊 Passes specifically for Task 4: {task4_passes}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")

def check_mt5_files_folder():
    """Check if there are any database files in MT5 Files folder"""
    
    print(f"\n🔍 CHECKING MT5 FILES FOLDER")
    print("=" * 40)
    
    # Common MT5 paths
    possible_paths = [
        r"C:\Users\<USER>\AppData\Roaming\MetaQuotes\Terminal\7BC3F33EDFDBDBDBADB45838B9A2D03F\MQL5\Files",
        r"C:\Program Files\MetaTrader 5\MQL5\Files",
        r"C:\Users\<USER>\AppData\Roaming\MetaQuotes\Terminal\Common\Files".format(os.getenv('USERNAME', 'User'))
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            print(f"✅ Found MT5 Files folder: {path}")
            
            # Look for database files
            files = os.listdir(path)
            db_files = [f for f in files if f.endswith('.sqlite')]
            
            if db_files:
                print(f"📁 Database files found: {db_files}")
                
                # Check if our database is there
                if 'database911.sqlite' in db_files:
                    db_path = os.path.join(path, 'database911.sqlite')
                    print(f"🎯 Found our database in MT5 Files: {db_path}")
                    
                    # Quick check of this database
                    try:
                        conn = sqlite3.connect(db_path)
                        cursor = conn.cursor()
                        cursor.execute("SELECT COUNT(*) FROM passes")
                        passes_count = cursor.fetchone()[0]
                        print(f"📊 Passes in MT5 database: {passes_count}")
                        
                        if passes_count > 0:
                            print("🎉 FOUND RESULTS IN MT5 DATABASE!")
                            cursor.execute("SELECT * FROM passes LIMIT 3")
                            sample = cursor.fetchall()
                            print(f"📊 Sample results: {sample}")
                        
                        conn.close()
                    except Exception as e:
                        print(f"❌ Error checking MT5 database: {e}")
            else:
                print(f"📁 No database files in {path}")
        else:
            print(f"❌ Path not found: {path}")

if __name__ == "__main__":
    diagnose_database()
    check_mt5_files_folder()
