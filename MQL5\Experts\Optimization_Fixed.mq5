//+------------------------------------------------------------------+
//|                                                 Optimization.mq5 |
//|                                        Copyright 2024, <PERSON><PERSON> |
//|                          https://www.mql5.com/en/users/antekov |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, <PERSON><PERSON>"
#property link      "https://www.mql5.com/en/users/antekov"
#property description "EA for projects auto optimization"

#define __VERSION__ "1.03"
#property version __VERSION__

// Fixed include path for organized structure
#include "MultiCurrency/Optimizer.mqh"

sinput string fileName_
      = "database911.sqlite";                        // - File with the main database
sinput string pythonPath_
      = "C:\\Python\\Python312\\python.exe";        // - Path to Python interpreter

COptimizer *optimizer;                               // Pointer to the optimizer object

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit() {
      // Connect to the main database
      DB::Test(fileName_);

      // Create an optimizer
      optimizer = new COptimizer(pythonPath_);

      // Create the timer and start its handler
      EventSetTimer(20);
      OnTimer();

      return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert timer function                                            |
//+------------------------------------------------------------------+
void OnTimer() {
      // Start the optimizer handling
      optimizer.Process();
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
      EventKillTimer();

      // Remove the optimizer
      if(!!optimizer) {
            delete optimizer;
      }
}
//+------------------------------------------------------------------+
