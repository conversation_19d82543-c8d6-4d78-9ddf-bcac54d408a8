#!/usr/bin/env python3
"""
Check Task 5 configuration
"""
import sqlite3

def check_task5():
    db_path = "Database/database911.sqlite"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔍 TASK 5 CONFIGURATION CHECK")
        print("=" * 50)
        
        # Get Task 5 details
        cursor.execute("""
            SELECT 
                t.id_task, t.status, t.id_job,
                j.id_job, j.id_stage, j.symbol, j.status as job_status,
                s.id_stage, s.name, s.expert, s.symbol as stage_symbol
            FROM tasks t
            JOIN jobs j ON t.id_job = j.id_job
            JOIN stages s ON j.id_stage = s.id_stage
            WHERE t.id_task = 5
        """)
        
        task5 = cursor.fetchone()
        if task5:
            print(f"📋 Task 5 Details:")
            print(f"  Task ID: {task5[0]}")
            print(f"  Task Status: {task5[1]}")
            print(f"  Job ID: {task5[2]}")
            print(f"  Stage ID: {task5[7]}")
            print(f"  Stage Name: {task5[8]}")
            print(f"  Expert: {task5[9]}")
            print(f"  Symbol: {task5[10]}")
            
            if task5[9] == "ClusteringStage1.py":
                print("✅ Correct: Task 5 should run Python clustering")
            else:
                print("❌ Wrong: Task 5 should run ClusteringStage1.py, not an EA")
        else:
            print("❌ Task 5 not found!")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    check_task5()
