# Multi-Currency FX EA Automation System - Python Dependencies
# Install with: pip install -r requirements.txt

# Core data manipulation and analysis
pandas>=1.5.0
numpy>=1.21.0

# Machine learning for clustering
scikit-learn>=1.1.0

# Database connectivity (usually included with Python)
# sqlite3 is part of Python standard library

# Optional: Enhanced data visualization (for future monitoring dashboard)
matplotlib>=3.5.0
seaborn>=0.11.0

# Optional: Web framework for monitoring dashboard
flask>=2.2.0
flask-cors>=3.0.10

# Optional: Enhanced JSON handling
ujson>=5.4.0

# Development and testing
pytest>=7.0.0
pytest-cov>=3.0.0
