# Multi-Currency FX EA Automation System - Setup Guide

## Directory Structure

Your files should be organized as follows:

### Current Development Directory (Keep for backup/development)
```
c:\Documents\Dev\Multi-Currency FX EA Automation\
├── MQL5\
│   ├── Experts\
│   │   ├── Optimization.mq5
│   │   ├── SimpleVolumesExpert.mq5
│   │   ├── SimpleVolumesStage1.mq5
│   │   ├── SimpleVolumesStage2.mq5
│   │   ├── SimpleVolumesStage3.mq5
│   │   ├── HistoryReceiverExpert.mq5
│   │   ├── SimpleHistoryReceiverExpert.mq5
│   │   └── LibraryExport.mq5
│   ├── Include\
│   │   ├── [All .mqh files]
│   │   ├── Database.mqh
│   │   ├── Optimizer.mqh
│   │   ├── OptimizerTask.mqh
│   │   ├── VirtualAdvisor.mqh
│   │   └── [etc...]
│   └── Scripts\
├── Python\
│   └── ClusteringStage1.py
├── Database\
│   ├── database.sqlite.schema.sql
│   └── database911.sqlite (will be created)
└── Documentation\
    └── README_SETUP.md (this file)
```

### MetaTrader 5 Terminal Directory (Copy files here for compilation)
```
%APPDATA%\MetaQuotes\Terminal\[YourTerminalID]\MQL5\
├── Experts\
│   ├── MultiCurrency\
│   │   ├── Optimization.mq5
│   │   ├── SimpleVolumesExpert.mq5
│   │   ├── SimpleVolumesStage1.mq5
│   │   ├── SimpleVolumesStage2.mq5
│   │   ├── SimpleVolumesStage3.mq5
│   │   ├── HistoryReceiverExpert.mq5
│   │   ├── SimpleHistoryReceiverExpert.mq5
│   │   └── LibraryExport.mq5
│   └── ClusteringStage1.py
├── Include\
│   ├── MultiCurrency\
│   │   ├── [All .mqh files from your project]
│   │   └── [Custom indicators: Asymmetric Compounding, DEMA-ATR]
└── Files\
    └── database911.sqlite
```

## Setup Steps

### Step 1: Create MetaTrader Directory Structure
1. Open MetaTrader 5
2. Press Ctrl+Shift+D to open Data Folder
3. Navigate to MQL5 folder
4. Create subdirectories as shown above

### Step 2: Copy Files to MetaTrader Directory
Copy all files from your development directory to the MetaTrader directory structure.

### Step 3: Database Setup
The database will be created in the next steps with proper schema and initial data.

### Step 4: Python Environment
Ensure Python is installed with required packages:
```bash
pip install pandas scikit-learn sqlite3
```

### Step 5: Compilation Order
1. Compile all .mqh files first (they're includes)
2. Compile Optimization.mq5
3. Compile other Expert Advisors
4. Test with simple chart loading

## Next Steps
Follow the detailed setup instructions in the subsequent files.
