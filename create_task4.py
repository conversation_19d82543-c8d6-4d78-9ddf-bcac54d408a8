#!/usr/bin/env python3
"""
Create Task 4 for EURUSD optimization
"""
import sqlite3

def create_task4():
    db_path = "Database/database911.sqlite"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # First, let's see the structure of existing tasks
        cursor.execute("SELECT * FROM tasks")
        existing_tasks = cursor.fetchall()
        print(f"📋 Existing tasks: {existing_tasks}")
        
        # Check jobs table structure
        cursor.execute("PRAGMA table_info(jobs)")
        job_columns = cursor.fetchall()
        print(f"📋 Jobs table columns: {[col[1] for col in job_columns]}")

        # Check if we need to create a job first
        cursor.execute("SELECT * FROM jobs")
        existing_jobs = cursor.fetchall()
        print(f"📋 Existing jobs: {existing_jobs}")

        # Create Job 4 for EURUSD if it doesn't exist
        cursor.execute("SELECT COUNT(*) FROM jobs WHERE id_job = 4")
        job_exists = cursor.fetchone()[0] > 0

        if not job_exists:
            # Use the correct column structure: id_job, id_stage, symbol, period, tester_inputs, status
            cursor.execute("""
                INSERT INTO jobs (id_job, id_stage, symbol, period, tester_inputs, status)
                VALUES (4, 4, 'EURUSD', 'H1', NULL, 'Processing')
            """)
            print("✅ Created Job 4")
        
        # Create Task 4
        cursor.execute("SELECT COUNT(*) FROM tasks WHERE id_task = 4")
        task_exists = cursor.fetchone()[0] > 0
        
        if not task_exists:
            cursor.execute("""
                INSERT INTO tasks (id_task, id_job, optimization_criterion, start_date, finish_date, status)
                VALUES (4, 4, -1, NULL, NULL, 'Processing')
            """)
            print("✅ Created Task 4")
        else:
            print("ℹ️ Task 4 already exists")
        
        # Commit changes
        conn.commit()
        
        # Verify creation
        cursor.execute("SELECT * FROM tasks WHERE id_task = 4")
        task4 = cursor.fetchone()
        print(f"✅ Task 4 created: {task4}")
        
        conn.close()
        print("🎉 Task 4 is ready!")
        
    except Exception as e:
        print(f"❌ Error creating Task 4: {e}")

if __name__ == "__main__":
    create_task4()
