#!/usr/bin/env python3
"""
Debug Compilation Issue
This script checks why the compilation is not picking up the fixed Database.mqh file.
"""

import os
from pathlib import Path

# Your MT5 Terminal Path
MT5_TERMINAL_PATH = r"C:\Users\<USER>\AppData\Roaming\MetaQuotes\Terminal\7BC3F33EDFDBDBDBADB45838B9A2D03F\MQL5"

def find_all_database_files():
    """Find all Database.mqh files in the terminal directory."""
    
    print("🔍 Searching for all Database.mqh files...")
    
    terminal_path = Path(MT5_TERMINAL_PATH)
    database_files = []
    
    # Search recursively for all Database.mqh files
    for file_path in terminal_path.rglob("Database.mqh"):
        database_files.append(file_path)
    
    print(f"📋 Found {len(database_files)} Database.mqh files:")
    
    for i, file_path in enumerate(database_files, 1):
        relative_path = file_path.relative_to(terminal_path)
        print(f"  {i}. {relative_path}")
        
        # Check file content
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if 'database911.sqlite' in content:
                print(f"     ✅ Contains 'database911.sqlite'")
            elif 'database.sqlite' in content:
                print(f"     ❌ Contains old 'database.sqlite'")
            elif ' d a t a b a s e ' in content:
                print(f"     ❌ Corrupted (spaces between characters)")
            else:
                print(f"     ⚠️ Unknown content")
                
        except Exception as e:
            print(f"     ❌ Error reading: {e}")
    
    return database_files

def check_optimization_includes():
    """Check what includes Optimization.mq5 is actually using."""
    
    print("\n🔍 Checking Optimization.mq5 includes...")
    
    opt_file = Path(MT5_TERMINAL_PATH) / "Experts" / "Optimization.mq5"
    
    if not opt_file.exists():
        print(f"❌ Optimization.mq5 not found: {opt_file}")
        return False
    
    try:
        with open(opt_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find include statements
        import re
        includes = re.findall(r'#include\s+"([^"]+)"', content)
        
        print(f"📋 Found {len(includes)} include statements:")
        for include in includes:
            print(f"  - {include}")
            
            # Check if this include file exists and what it contains
            if 'Database' in include:
                include_path = Path(MT5_TERMINAL_PATH) / "Include" / "MultiCurrency" / "Database.mqh"
                if include_path.exists():
                    with open(include_path, 'r', encoding='utf-8') as f:
                        db_content = f.read()
                    if 'database911.sqlite' in db_content:
                        print(f"    ✅ Database.mqh contains correct filename")
                    else:
                        print(f"    ❌ Database.mqh has wrong filename")
        
        return True
        
    except Exception as e:
        print(f"❌ Error reading Optimization.mq5: {e}")
        return False

def check_compiled_files():
    """Check when files were last compiled."""
    
    print("\n🔍 Checking compilation timestamps...")
    
    files_to_check = [
        Path(MT5_TERMINAL_PATH) / "Experts" / "Optimization.ex5",
        Path(MT5_TERMINAL_PATH) / "Include" / "MultiCurrency" / "Database.mqh",
        Path(MT5_TERMINAL_PATH) / "Experts" / "Optimization.mq5"
    ]
    
    for file_path in files_to_check:
        if file_path.exists():
            stat = file_path.stat()
            mod_time = stat.st_mtime
            import datetime
            mod_datetime = datetime.datetime.fromtimestamp(mod_time)
            print(f"  📅 {file_path.name}: {mod_datetime}")
        else:
            print(f"  ❌ {file_path.name}: Not found")

def force_clean_compilation():
    """Force a clean compilation by removing .ex5 files."""
    
    print("\n🔧 Forcing clean compilation...")
    
    ex5_files = [
        Path(MT5_TERMINAL_PATH) / "Experts" / "Optimization.ex5"
    ]
    
    for ex5_file in ex5_files:
        if ex5_file.exists():
            try:
                ex5_file.unlink()
                print(f"  🗑️ Deleted: {ex5_file.name}")
            except Exception as e:
                print(f"  ❌ Could not delete {ex5_file.name}: {e}")
        else:
            print(f"  ℹ️ {ex5_file.name} already deleted")

def create_test_database_connection():
    """Create a simple test to verify database connection."""
    
    print("\n🧪 Creating database connection test...")
    
    test_content = '''//+------------------------------------------------------------------+
//|                                                DatabaseTest.mq5 |
//+------------------------------------------------------------------+
#include "../Include/MultiCurrency/Database.mqh"

void OnInit() {
    Print("Testing database connection...");
    
    // Try to connect to database
    if(DB::Connect("database911.sqlite")) {
        Print("✅ Database connection successful!");
        Print("Database file: ", DB::FileName(true));
        DB::Close();
    } else {
        Print("❌ Database connection failed!");
        Print("Looking for: ", DB::FileName(true));
    }
}

void OnTick() {
    // Do nothing
}
'''
    
    test_file = Path(MT5_TERMINAL_PATH) / "Experts" / "DatabaseTest.mq5"
    
    try:
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_content)
        print(f"  ✅ Created test file: {test_file}")
        print("  📝 Compile DatabaseTest.mq5 in MetaEditor to test database connection")
        return True
    except Exception as e:
        print(f"  ❌ Error creating test file: {e}")
        return False

def main():
    """Main function."""
    
    print("🔍 Compilation Issue Debug")
    print("=" * 50)
    
    # Find all Database.mqh files
    database_files = find_all_database_files()
    
    # Check Optimization.mq5 includes
    check_optimization_includes()
    
    # Check compilation timestamps
    check_compiled_files()
    
    # Force clean compilation
    force_clean_compilation()
    
    # Create test file
    create_test_database_connection()
    
    print("\n🎯 Recommendations:")
    print("1. Compile DatabaseTest.mq5 first to test database connection")
    print("2. If that works, recompile Optimization.mq5 (should be forced clean)")
    print("3. If still failing, there might be multiple Database.mqh files")
    print("4. Check MetaEditor compilation output for any include errors")
    
    return True

if __name__ == "__main__":
    if main():
        input("\nPress Enter to continue...")
    else:
        input("\nPress Enter to exit...")
