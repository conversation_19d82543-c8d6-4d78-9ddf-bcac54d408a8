#!/usr/bin/env python3
"""
Check System Status
This script checks the database and system status to verify everything is working.
"""

import sqlite3
from pathlib import Path

# Your MT5 Terminal Path
MT5_TERMINAL_PATH = r"C:\Users\<USER>\AppData\Roaming\MetaQuotes\Terminal\7BC3F33EDFDBDBDBADB45838B9A2D03F\MQL5"

def check_database_status():
    """Check the current database status."""
    
    print("🗄️ Checking database status...")
    
    db_path = Path(MT5_TERMINAL_PATH) / "Files" / "database911.sqlite"
    
    if not db_path.exists():
        print(f"❌ Database file not found: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        print(f"✅ Database connected: {db_path}")
        print(f"📊 Database size: {db_path.stat().st_size} bytes")
        
        # Check projects
        cursor.execute("SELECT id_project, name, status FROM projects ORDER BY id_project;")
        projects = cursor.fetchall()
        print(f"\n📋 Projects ({len(projects)}):")
        for project in projects:
            print(f"  {project[0]}: {project[1]} - Status: {project[2]}")
        
        # Check stages with status
        cursor.execute("""
            SELECT s.id_stage, s.id_project, s.name, s.symbol, s.status, p.name as project_name
            FROM stages s 
            JOIN projects p ON s.id_project = p.id_project 
            WHERE s.name != 'Single tester pass'
            ORDER BY s.id_project, s.id_stage;
        """)
        stages = cursor.fetchall()
        print(f"\n🎯 Optimization Stages ({len(stages)}):")
        for stage in stages:
            print(f"  Stage {stage[0]} ({stage[5]}): {stage[2]} - {stage[3]} - Status: {stage[4]}")
        
        # Check for queued tasks
        cursor.execute("""
            SELECT t.id_task, t.status, s.name as stage_name, s.symbol, p.name as project_name
            FROM tasks t
            JOIN jobs j ON t.id_job = j.id_job
            JOIN stages s ON j.id_stage = s.id_stage
            JOIN projects p ON s.id_project = p.id_project
            WHERE t.status = 'Queued'
            ORDER BY t.id_task;
        """)
        queued_tasks = cursor.fetchall()
        print(f"\n⏳ Queued Tasks ({len(queued_tasks)}):")
        if queued_tasks:
            for task in queued_tasks:
                print(f"  Task {task[0]}: {task[4]} - {task[2]} - {task[3]} - Status: {task[1]}")
        else:
            print("  No queued tasks found")
        
        # Check for processing tasks
        cursor.execute("""
            SELECT t.id_task, t.status, s.name as stage_name, s.symbol, p.name as project_name
            FROM tasks t
            JOIN jobs j ON t.id_job = j.id_job
            JOIN stages s ON j.id_stage = s.id_stage
            JOIN projects p ON s.id_project = p.id_project
            WHERE t.status = 'Processing'
            ORDER BY t.id_task;
        """)
        processing_tasks = cursor.fetchall()
        print(f"\n🔄 Processing Tasks ({len(processing_tasks)}):")
        if processing_tasks:
            for task in processing_tasks:
                print(f"  Task {task[0]}: {task[4]} - {task[2]} - {task[3]} - Status: {task[1]}")
        else:
            print("  No processing tasks found")
        
        conn.close()
        return True
        
    except sqlite3.Error as e:
        print(f"❌ Database error: {e}")
        return False

def check_files():
    """Check if all required files are in place."""
    
    print("\n📁 Checking required files...")
    
    files_to_check = [
        ("Files/database911.sqlite", "Database file"),
        ("Experts/ClusteringStage1.py", "Python clustering script"),
        ("Experts/Optimization.ex5", "Main optimization EA (compiled)"),
        ("Include/MultiCurrency/Optimizer.mqh", "Optimizer include file"),
        ("Include/fxsaber/MultiTester/MTTester.mqh", "MTTester stub file")
    ]
    
    all_good = True
    for file_path, description in files_to_check:
        full_path = Path(MT5_TERMINAL_PATH) / file_path
        if full_path.exists():
            size = full_path.stat().st_size
            print(f"  ✅ {description} ({size} bytes)")
        else:
            print(f"  ❌ Missing: {description}")
            all_good = False
    
    return all_good

def show_next_steps():
    """Show what should happen next."""
    
    print("\n🚀 Expected System Behavior:")
    print("=" * 50)
    print("1. Load Optimization.ex5 on any chart in MT5")
    print("2. Check Expert tab for initialization messages")
    print("3. System should find queued tasks and start processing")
    print("4. For EURUSD Stage 1: System will attempt to start optimization")
    print("5. Python clustering will be called when Stage 1 completes")
    print("\n📋 What to Look For in Expert Tab:")
    print("- 'Database connected successfully'")
    print("- 'Starting optimization task...'")
    print("- Task ID and processing messages")
    print("- No error messages about missing files")
    print("\n⚠️ Note: Strategy Tester automation is currently using stub implementation")
    print("   The system will log messages but won't automatically open the tester yet.")

def main():
    """Main function."""
    
    print("📊 Multi-Currency FX EA System Status Check")
    print("=" * 60)
    
    # Check database
    if not check_database_status():
        print("\n❌ Database check failed!")
        return False
    
    # Check files
    if not check_files():
        print("\n⚠️ Some files are missing!")
    
    # Show next steps
    show_next_steps()
    
    print("\n✅ System status check completed!")
    return True

if __name__ == "__main__":
    if main():
        input("\nPress Enter to continue...")
    else:
        input("\nPress Enter to exit...")
