#!/usr/bin/env python3
"""
Multi-Currency FX EA Automation System - Database Setup Script
This script initializes the SQLite database with the proper schema and initial data.
"""

import sqlite3
import os
import sys
from pathlib import Path

def setup_database():
    """Initialize the database with schema and initial data."""
    
    # Database file path (will be created in the same directory as this script)
    db_path = Path(__file__).parent / "database911.sqlite"
    sql_path = Path(__file__).parent / "init_database.sql"
    
    print(f"Setting up database at: {db_path}")
    print(f"Using SQL script: {sql_path}")
    
    # Remove existing database if it exists
    if db_path.exists():
        print("Removing existing database...")
        db_path.unlink()
    
    # Check if SQL script exists
    if not sql_path.exists():
        print(f"ERROR: SQL script not found at {sql_path}")
        return False
    
    try:
        # Create new database and execute schema
        print("Creating new database...")
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # Read and execute SQL script
        print("Executing database schema...")
        with open(sql_path, 'r', encoding='utf-8') as f:
            sql_script = f.read()
        
        # Execute the entire script
        cursor.executescript(sql_script)
        
        # Commit changes
        conn.commit()
        
        # Verify the setup by checking tables
        print("Verifying database setup...")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        expected_tables = ['projects', 'stages', 'jobs', 'tasks', 'passes', 'strategy_groups', 'passes_clusters']
        found_tables = [table[0] for table in tables]
        
        print(f"Found tables: {found_tables}")
        
        for table in expected_tables:
            if table in found_tables:
                print(f"[OK] Table '{table}' created successfully")
            else:
                print(f"[ERROR] Table '{table}' missing!")
                return False

        # Check if initial data was inserted
        cursor.execute("SELECT COUNT(*) FROM projects;")
        project_count = cursor.fetchone()[0]
        print(f"[OK] {project_count} projects inserted")

        cursor.execute("SELECT COUNT(*) FROM stages;")
        stage_count = cursor.fetchone()[0]
        print(f"[OK] {stage_count} stages inserted")

        conn.close()

        print(f"\n[SUCCESS] Database setup completed successfully!")
        print(f"Database location: {db_path}")
        print(f"Database size: {db_path.stat().st_size} bytes")

        return True

    except sqlite3.Error as e:
        print(f"[ERROR] Database error: {e}")
        return False
    except Exception as e:
        print(f"[ERROR] Unexpected error: {e}")
        return False

def verify_database():
    """Verify the database structure and data."""
    
    db_path = Path(__file__).parent / "database911.sqlite"
    
    if not db_path.exists():
        print("[ERROR] Database file not found!")
        return False
    
    try:
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        print("\n📊 Database Verification Report:")
        print("=" * 50)
        
        # Check projects
        cursor.execute("SELECT id_project, name, status FROM projects;")
        projects = cursor.fetchall()
        print(f"\nProjects ({len(projects)}):")
        for project in projects:
            print(f"  {project[0]}: {project[1]} - {project[2]}")
        
        # Check stages
        cursor.execute("SELECT id_stage, id_project, name, symbol, status FROM stages WHERE name != 'Single tester pass';")
        stages = cursor.fetchall()
        print(f"\nStages ({len(stages)}):")
        for stage in stages:
            print(f"  Stage {stage[0]} (Project {stage[1]}): {stage[2]} - {stage[3]} - {stage[4]}")
        
        # Check triggers
        cursor.execute("SELECT name FROM sqlite_master WHERE type='trigger';")
        triggers = cursor.fetchall()
        print(f"\nTriggers ({len(triggers)}):")
        for trigger in triggers:
            print(f"  [OK] {trigger[0]}")

        conn.close()
        return True

    except sqlite3.Error as e:
        print(f"[ERROR] Database verification error: {e}")
        return False

if __name__ == "__main__":
    print("Multi-Currency FX EA Automation System - Database Setup")
    print("=" * 60)
    
    # Setup database
    if setup_database():
        # Verify setup
        verify_database()
        print("\n[SUCCESS] Database setup and verification completed!")
        print("\nNext steps:")
        print("1. Copy database911.sqlite to your MT5 terminal's Files folder")
        print("2. Compile the MQL5 Expert Advisors")
        print("3. Load Optimization.ex5 on a chart to start the automation")
    else:
        print("\n[ERROR] Database setup failed!")
        sys.exit(1)
