#!/usr/bin/env python3
"""
Fix ExpertHistory Include
This script fixes the remaining ExpertHistory.mqh include path.
"""

import re
from pathlib import Path

# Your MT5 Terminal Path
MT5_TERMINAL_PATH = r"C:\Users\<USER>\AppData\Roaming\MetaQuotes\Terminal\7BC3F33EDFDBDBDBADB45838B9A2D03F\MQL5"

def fix_experthistory_include():
    """Fix the ExpertHistory.mqh include path in SimpleVolumesExpert.mq5."""
    
    print("🔧 Fixing ExpertHistory.mqh include path...")
    
    file_path = Path(MT5_TERMINAL_PATH) / "Experts" / "SimpleVolumesExpert.mq5"
    
    if not file_path.exists():
        print(f"❌ File not found: {file_path}")
        return False
    
    try:
        # Read the file
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Fix the ExpertHistory.mqh include
        content = re.sub(
            r'#include\s+"ExpertHistory\.mqh"',
            r'#include "../Include/MultiCurrency/ExpertHistory.mqh"',
            content
        )
        
        # Also fix any other standalone includes that might be missing the path
        standalone_includes = [
            'GroupsLibrary.mqh',
            'ExportedGroupsLibrary.mqh'
        ]
        
        for include_file in standalone_includes:
            pattern = rf'#include\s+"{re.escape(include_file)}"'
            replacement = f'#include "../Include/MultiCurrency/{include_file}"'
            content = re.sub(pattern, replacement, content)
        
        if content != original_content:
            # Backup the original file
            backup_path = file_path.with_suffix('.mq5.backup')
            with open(backup_path, 'w', encoding='utf-8') as f:
                f.write(original_content)
            
            # Write the fixed content
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"  ✅ Fixed ExpertHistory include path in SimpleVolumesExpert.mq5")
            print(f"  📋 Backup saved as: {backup_path.name}")
            return True
        else:
            print(f"  ℹ️ No changes needed in SimpleVolumesExpert.mq5")
            return True
            
    except Exception as e:
        print(f"  ❌ Error fixing SimpleVolumesExpert.mq5: {e}")
        return False

def verify_all_includes():
    """Verify that all required include files exist."""
    
    print("\n🔍 Verifying all include files exist...")
    
    include_dir = Path(MT5_TERMINAL_PATH) / "Include" / "MultiCurrency"
    
    required_files = [
        'ExpertHistory.mqh',
        'GroupsLibrary.mqh', 
        'ExportedGroupsLibrary.mqh',
        'VirtualFactory.mqh',
        'VirtualAdvisor.mqh',
        'Money.mqh',
        'Database.mqh'
    ]
    
    all_good = True
    
    for file_name in required_files:
        file_path = include_dir / file_name
        if file_path.exists():
            print(f"  ✅ {file_name}")
        else:
            print(f"  ❌ Missing: {file_name}")
            all_good = False
    
    return all_good

def main():
    """Main function."""
    
    print("🔧 ExpertHistory Include Fix")
    print("=" * 40)
    
    # Check if terminal path exists
    if not Path(MT5_TERMINAL_PATH).exists():
        print(f"❌ MT5 Terminal path not found: {MT5_TERMINAL_PATH}")
        return False
    
    # Fix the include path
    if not fix_experthistory_include():
        return False
    
    # Verify all includes exist
    if not verify_all_includes():
        print("\n⚠️ Warning: Some include files are missing!")
    
    print("\n🎉 ExpertHistory include path fixed!")
    print("\nNext steps:")
    print("1. Open MetaEditor (F4 in MT5)")
    print("2. Try compiling SimpleVolumesExpert.mq5")
    print("3. Should now compile with 0 errors")
    print("4. Then compile other Expert Advisors")
    print("5. Finally test the complete system")
    
    return True

if __name__ == "__main__":
    if main():
        input("\nPress Enter to continue...")
    else:
        input("\nPress Enter to exit...")
