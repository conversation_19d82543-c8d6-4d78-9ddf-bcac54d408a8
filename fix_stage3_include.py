#!/usr/bin/env python3
"""
Fix Stage3 Include
This script fixes the ExpertHistory.mqh include path in SimpleVolumesStage3.mq5.
"""

import re
from pathlib import Path

# Your MT5 Terminal Path
MT5_TERMINAL_PATH = r"C:\Users\<USER>\AppData\Roaming\MetaQuotes\Terminal\7BC3F33EDFDBDBDBADB45838B9A2D03F\MQL5"

def fix_stage3_include():
    """Fix the ExpertHistory.mqh include path in SimpleVolumesStage3.mq5."""
    
    print("🔧 Fixing ExpertHistory.mqh include path in SimpleVolumesStage3.mq5...")
    
    file_path = Path(MT5_TERMINAL_PATH) / "Experts" / "SimpleVolumesStage3.mq5"
    
    if not file_path.exists():
        print(f"❌ File not found: {file_path}")
        return False
    
    try:
        # Read the file
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Fix the ExpertHistory.mqh include
        content = re.sub(
            r'#include\s+"ExpertHistory\.mqh"',
            r'#include "../Include/MultiCurrency/ExpertHistory.mqh"',
            content
        )
        
        # Also fix any other standalone includes that might be missing the path
        standalone_includes = [
            'GroupsLibrary.mqh',
            'ExportedGroupsLibrary.mqh'
        ]
        
        for include_file in standalone_includes:
            pattern = rf'#include\s+"{re.escape(include_file)}"'
            replacement = f'#include "../Include/MultiCurrency/{include_file}"'
            content = re.sub(pattern, replacement, content)
        
        if content != original_content:
            # Backup the original file
            backup_path = file_path.with_suffix('.mq5.backup')
            with open(backup_path, 'w', encoding='utf-8') as f:
                f.write(original_content)
            
            # Write the fixed content
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"  ✅ Fixed ExpertHistory include path in SimpleVolumesStage3.mq5")
            print(f"  📋 Backup saved as: {backup_path.name}")
            return True
        else:
            print(f"  ℹ️ No changes needed in SimpleVolumesStage3.mq5")
            return True
            
    except Exception as e:
        print(f"  ❌ Error fixing SimpleVolumesStage3.mq5: {e}")
        return False

def fix_all_remaining_includes():
    """Fix ExpertHistory includes in all remaining .mq5 files."""
    
    print("\n🔧 Checking all .mq5 files for ExpertHistory includes...")
    
    experts_dir = Path(MT5_TERMINAL_PATH) / "Experts"
    mq5_files = list(experts_dir.glob("SimpleVolumes*.mq5"))
    
    fixed_count = 0
    
    for mq5_file in mq5_files:
        try:
            with open(mq5_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # Fix ExpertHistory include if it exists
            content = re.sub(
                r'#include\s+"ExpertHistory\.mqh"',
                r'#include "../Include/MultiCurrency/ExpertHistory.mqh"',
                content
            )
            
            if content != original_content:
                with open(mq5_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"  ✅ Fixed ExpertHistory include in {mq5_file.name}")
                fixed_count += 1
            else:
                print(f"  ℹ️ No ExpertHistory include found in {mq5_file.name}")
                
        except Exception as e:
            print(f"  ❌ Error processing {mq5_file.name}: {e}")
    
    print(f"\n📊 Fixed ExpertHistory includes in {fixed_count} files")
    return True

def main():
    """Main function."""
    
    print("🔧 Stage3 Include Fix")
    print("=" * 30)
    
    # Check if terminal path exists
    if not Path(MT5_TERMINAL_PATH).exists():
        print(f"❌ MT5 Terminal path not found: {MT5_TERMINAL_PATH}")
        return False
    
    # Fix the specific file
    if not fix_stage3_include():
        return False
    
    # Fix all remaining files
    if not fix_all_remaining_includes():
        return False
    
    print("\n🎉 All ExpertHistory include paths fixed!")
    print("\nNext steps:")
    print("1. Open MetaEditor (F4 in MT5)")
    print("2. Try compiling SimpleVolumesStage3.mq5")
    print("3. Should now compile with 0 errors")
    print("4. Compile all other SimpleVolumes*.mq5 files")
    print("5. All Expert Advisors should now compile successfully!")
    
    return True

if __name__ == "__main__":
    if main():
        input("\nPress Enter to continue...")
    else:
        input("\nPress Enter to exit...")
