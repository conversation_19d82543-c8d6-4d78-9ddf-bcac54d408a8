#!/usr/bin/env python3
"""
Fix Database Location
This script copies the database to the correct location where the system is looking for it.
"""

import shutil
from pathlib import Path

# Your MT5 Terminal Path
MT5_TERMINAL_PATH = r"C:\Users\<USER>\AppData\Roaming\MetaQuotes\Terminal\7BC3F33EDFDBDBDBADB45838B9A2D03F\MQL5"

def copy_database_to_common():
    """Copy database to the Common\Files directory where the system is looking."""
    
    print("🔧 Copying database to Common directory...")
    
    # Source database (terminal-specific Files directory)
    source_db = Path(MT5_TERMINAL_PATH) / "Files" / "database911.sqlite"
    
    # Destination (Common Files directory)
    common_path = Path(MT5_TERMINAL_PATH).parent.parent / "Common" / "Files"
    dest_db = common_path / "database911.sqlite"
    
    print(f"📍 Source: {source_db}")
    print(f"📍 Destination: {dest_db}")
    
    if not source_db.exists():
        print(f"❌ Source database not found: {source_db}")
        return False
    
    # Create Common/Files directory if it doesn't exist
    dest_db.parent.mkdir(parents=True, exist_ok=True)
    
    # Copy the database
    shutil.copy2(source_db, dest_db)
    
    print(f"✅ Database copied to Common directory")
    print(f"📊 File size: {dest_db.stat().st_size} bytes")
    
    return True

def verify_database_in_common():
    """Verify the database exists in Common directory and has correct tables."""
    
    print("\n🔍 Verifying database in Common directory...")
    
    common_path = Path(MT5_TERMINAL_PATH).parent.parent / "Common" / "Files"
    db_path = common_path / "database911.sqlite"
    
    if not db_path.exists():
        print(f"❌ Database not found in Common directory: {db_path}")
        return False
    
    try:
        import sqlite3
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # Check if tasks table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='tasks';")
        result = cursor.fetchone()
        
        if result:
            print(f"✅ 'tasks' table found in Common database")
            
            # Check number of tasks
            cursor.execute("SELECT COUNT(*) FROM tasks;")
            task_count = cursor.fetchone()[0]
            print(f"📊 Tasks in database: {task_count}")
            
            # Check queued tasks
            cursor.execute("SELECT COUNT(*) FROM tasks WHERE status = 'Queued';")
            queued_count = cursor.fetchone()[0]
            print(f"⏳ Queued tasks: {queued_count}")
            
            conn.close()
            return True
        else:
            print(f"❌ 'tasks' table not found in Common database")
            conn.close()
            return False
            
    except Exception as e:
        print(f"❌ Error checking Common database: {e}")
        return False

def create_database_location_test():
    """Create a test to verify the system can now find the database."""
    
    print("\n🧪 Creating database location verification test...")
    
    test_content = '''//+------------------------------------------------------------------+
//|                                          DatabaseLocationTest.mq5 |
//+------------------------------------------------------------------+
#include "../Include/MultiCurrency/Database.mqh"

void OnInit() {
    Print("Testing database location and tables...");
    
    // Try to connect to database
    if(DB::Connect("database911.sqlite")) {
        Print("✅ Database connection successful!");
        Print("Database file: ", DB::FileName(true));
        
        // Test a simple query to tasks table
        int request = DatabasePrepare(DB::Id(), "SELECT COUNT(*) FROM tasks;");
        if(request != INVALID_HANDLE) {
            struct Row {
                int count;
            } row;
            
            if(DatabaseReadBind(request, row)) {
                Print("✅ Tasks table found! Total tasks: ", row.count);
            } else {
                Print("❌ Could not read from tasks table");
            }
            DatabaseFinalize(request);
        } else {
            Print("❌ Could not query tasks table - table might not exist");
        }
        
        DB::Close();
    } else {
        Print("❌ Database connection failed!");
        Print("Looking for: ", DB::FileName(true));
    }
}

void OnTick() {
    // Do nothing
}
'''
    
    test_file = Path(MT5_TERMINAL_PATH) / "Experts" / "DatabaseLocationTest.mq5"
    
    try:
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_content)
        print(f"  ✅ Created test file: {test_file}")
        return True
    except Exception as e:
        print(f"  ❌ Error creating test file: {e}")
        return False

def main():
    """Main function."""
    
    print("🔧 Database Location Fix")
    print("=" * 40)
    
    # Copy database to Common directory
    if not copy_database_to_common():
        return False
    
    # Verify database in Common directory
    if not verify_database_in_common():
        print("\n⚠️ Warning: Database verification failed!")
        return False
    
    # Create verification test
    if not create_database_location_test():
        print("\n⚠️ Warning: Could not create test file!")
    
    print("\n🎉 Database location has been fixed!")
    print("\nNext steps:")
    print("1. Compile DatabaseLocationTest.mq5 to verify tables are accessible")
    print("2. If that works, test Optimization.mq5 again")
    print("3. The system should now find Task ID 4 instead of 0")
    print("4. Watch for 'Current Task ID = 4' in the Expert tab")
    
    return True

if __name__ == "__main__":
    if main():
        input("\nPress Enter to continue...")
    else:
        input("\nPress Enter to exit...")
