#!/usr/bin/env python3
"""
Fix Optimization.mq5 File
This script creates a clean, working Optimization.mq5 file in your MT5 terminal.
"""

from pathlib import Path

# Your MT5 Terminal Path
MT5_TERMINAL_PATH = r"C:\Users\<USER>\AppData\Roaming\MetaQuotes\Terminal\7BC3F33EDFDBDBDBADB45838B9A2D03F\MQL5"

def create_clean_optimization_file():
    """Create a clean, working Optimization.mq5 file."""
    
    print("🔧 Creating clean Optimization.mq5 file...")
    
    # Clean Optimization.mq5 content with correct include path
    optimization_content = '''//+------------------------------------------------------------------+
//|                                                 Optimization.mq5 |
//|                                        Copyright 2024, <PERSON><PERSON> |
//|                          https://www.mql5.com/en/users/antekov |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Yuriy Bykov"
#property link      "https://www.mql5.com/en/users/antekov"
#property description "EA for projects auto optimization"

#define __VERSION__ "1.03"
#property version __VERSION__

// Correct include path - note the forward slash and no MultiCurrency prefix
#include "..\\\\Include\\\\MultiCurrency\\\\Optimizer.mqh"

sinput string fileName_
      = "database911.sqlite";                        // - File with the main database
sinput string pythonPath_
      = "C:\\\\Python\\\\Python312\\\\python.exe";        // - Path to Python interpreter

COptimizer *optimizer;                               // Pointer to the optimizer object

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit() {
      // Connect to the main database
      DB::Test(fileName_);

      // Create an optimizer
      optimizer = new COptimizer(pythonPath_);

      // Create the timer and start its handler
      EventSetTimer(20);
      OnTimer();

      return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert timer function                                            |
//+------------------------------------------------------------------+
void OnTimer() {
      // Start the optimizer handling
      optimizer.Process();
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
      EventKillTimer();

      // Remove the optimizer
      if(!!optimizer) {
            delete optimizer;
      }
}
//+------------------------------------------------------------------+'''
    
    # Path to the Optimization.mq5 file in terminal
    optimization_file = Path(MT5_TERMINAL_PATH) / "Experts" / "Optimization.mq5"
    
    # Backup existing file if it exists
    if optimization_file.exists():
        backup_file = optimization_file.with_suffix('.mq5.backup')
        optimization_file.rename(backup_file)
        print(f"  📋 Backed up existing file to: {backup_file.name}")
    
    # Write the clean file
    with open(optimization_file, 'w', encoding='utf-8') as f:
        f.write(optimization_content)
    
    print(f"  ✅ Created clean Optimization.mq5")
    print(f"  📍 Location: {optimization_file}")
    
    return True

def verify_include_files():
    """Verify that the include files exist in the correct location."""
    
    print("\n🔍 Verifying include files...")
    
    include_dir = Path(MT5_TERMINAL_PATH) / "Include" / "MultiCurrency"
    
    required_files = [
        "Optimizer.mqh",
        "Database.mqh",
        "OptimizerTask.mqh"
    ]
    
    all_good = True
    for file in required_files:
        file_path = include_dir / file
        if file_path.exists():
            print(f"  ✅ {file}")
        else:
            print(f"  ❌ Missing: {file}")
            all_good = False
    
    if all_good:
        print("  ✅ All required include files found")
    else:
        print("  ❌ Some include files are missing")
    
    return all_good

def check_database_file():
    """Check if database file exists."""
    
    print("\n🗄️ Checking database file...")
    
    db_file = Path(MT5_TERMINAL_PATH) / "Files" / "database911.sqlite"
    
    if db_file.exists():
        print(f"  ✅ Database file found: {db_file}")
        print(f"  📊 Size: {db_file.stat().st_size} bytes")
        return True
    else:
        print(f"  ❌ Database file not found: {db_file}")
        return False

def main():
    """Main function."""
    
    print("🔧 Optimization.mq5 File Fixer")
    print("=" * 50)
    
    # Check if terminal path exists
    if not Path(MT5_TERMINAL_PATH).exists():
        print(f"❌ MT5 Terminal path not found: {MT5_TERMINAL_PATH}")
        return False
    
    # Create clean Optimization.mq5
    if not create_clean_optimization_file():
        return False
    
    # Verify include files
    if not verify_include_files():
        print("\n⚠️ Warning: Some include files are missing!")
        print("You may need to copy them again from your development directory.")
    
    # Check database
    if not check_database_file():
        print("\n⚠️ Warning: Database file is missing!")
        print("You may need to copy it again from Database/database911.sqlite")
    
    print("\n🎉 Optimization.mq5 file is ready for compilation!")
    print("\nNext steps:")
    print("1. Open MetaEditor (F4 in MT5)")
    print("2. Navigate to Experts folder")
    print("3. Open and compile Optimization.mq5")
    print("4. Look for '0 error(s), 0 warning(s)' in compilation log")
    
    return True

if __name__ == "__main__":
    if main():
        input("\nPress Enter to continue...")
    else:
        input("\nPress Enter to exit...")
