#!/usr/bin/env python3
"""
Fix Include Paths in MT5 Terminal
This script fixes the include paths in the copied files in your MT5 terminal directory.
"""

import re
from pathlib import Path

# Your MT5 Terminal Path
MT5_TERMINAL_PATH = r"C:\Users\<USER>\AppData\Roaming\MetaQuotes\Terminal\7BC3F33EDFDBDBDBADB45838B9A2D03F\MQL5"

def fix_include_paths_in_terminal():
    """Fix include paths in Expert Advisor files in the terminal."""
    
    print("🔧 Fixing include paths in MT5 terminal files...")
    
    experts_dir = Path(MT5_TERMINAL_PATH) / "Experts"
    
    if not experts_dir.exists():
        print(f"❌ Experts directory not found: {experts_dir}")
        return False
    
    # Include files that need the MultiCurrency/ prefix
    include_files = [
        "Advisor.mqh",
        "Database.mqh", 
        "GroupsLibrary.mqh",
        "Money.mqh",
        "Optimizer.mqh",
        "OptimizerTask.mqh",
        "Strategy.mqh",
        "TesterHandler.mqh",
        "VirtualAdvisor.mqh",
        "VirtualInterface.mqh",
        "VirtualOrder.mqh",
        "VirtualReceiver.mqh",
        "VirtualRiskManager.mqh",
        "VirtualStrategy.mqh",
        "SimpleVolumesStrategy.mqh",
        "HistoryStrategy.mqh",
        "VirtualChartOrder.mqh",
        "VirtualFactory.mqh",
        "VirtualHistoryAdvisor.mqh",
        "VirtualStrategyGroup.mqh",
        "VirtualSymbolReceiver.mqh",
        "ExpertHistory.mqh",
        "ExportedGroupsLibrary.mqh",
        "Factorable.mqh",
        "Interface.mqh",
        "Macros.mqh",
        "NewBarEvent.mqh",
        "Receiver.mqh"
    ]
    
    # Get all .mq5 files
    mq5_files = list(experts_dir.glob("*.mq5"))
    
    print(f"Found {len(mq5_files)} Expert Advisor files to fix:")
    
    for mq5_file in mq5_files:
        print(f"  📄 Processing: {mq5_file.name}")
        
        try:
            # Read the file with different encodings
            content = None
            for encoding in ['utf-8', 'utf-16', 'cp1252', 'latin1']:
                try:
                    with open(mq5_file, 'r', encoding=encoding) as f:
                        content = f.read()
                    break
                except UnicodeDecodeError:
                    continue
            
            if content is None:
                print(f"    ❌ Could not read file")
                continue
            
            # Track changes
            changes_made = 0
            
            # Fix include paths for each include file
            for include_file in include_files:
                # Pattern to match #include "filename.mqh" but not #include "MultiCurrency/filename.mqh"
                pattern = rf'#include\s+"(?!MultiCurrency/){re.escape(include_file)}"'
                replacement = f'#include "MultiCurrency/{include_file}"'
                
                # Count matches before replacement
                matches = len(re.findall(pattern, content))
                if matches > 0:
                    content = re.sub(pattern, replacement, content)
                    changes_made += matches
            
            # Write the file back if changes were made
            if changes_made > 0:
                with open(mq5_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"    ✅ Fixed {changes_made} include paths")
            else:
                print(f"    ℹ️ No changes needed")
                
        except Exception as e:
            print(f"    ❌ Error: {e}")
    
    return True

def create_compilation_instructions():
    """Create compilation instructions file."""
    
    instructions = f"""# Compilation Instructions for MT5 Terminal

## Files Location
All files have been copied to: {MT5_TERMINAL_PATH}

## Compilation Steps

### 1. Open MetaEditor
- In MetaTrader 5, press F4 or go to Tools → MetaQuotes Language Editor

### 2. Navigate to Files
- In MetaEditor Navigator, expand "Experts" folder
- You should see all the .mq5 files

### 3. Compilation Order
1. **First**: Compile Optimization.mq5 ⭐ (Most important)
2. **Then**: Compile other Expert Advisors:
   - SimpleVolumesExpert.mq5
   - SimpleVolumesStage1.mq5
   - SimpleVolumesStage2.mq5
   - SimpleVolumesStage3.mq5
   - HistoryReceiverExpert.mq5
   - SimpleHistoryReceiverExpert.mq5
   - LibraryExport.mq5

### 4. Check for Compilation Success
- Look for "0 error(s), 0 warning(s)" in the compilation log
- .ex5 files should appear in the Experts folder

### 5. Load the System
1. In MT5, open any chart (EURUSD recommended)
2. Drag Optimization.ex5 onto the chart
3. In EA settings:
   - Allow DLL imports: ✅
   - Allow WebRequest: ✅
   - Allow external experts: ✅

### 6. Verify System Start
- Check Expert tab for "Database connected successfully"
- Look for "Starting optimization task..." messages

## Database Status
✅ Database file: {MT5_TERMINAL_PATH}\\Files\\database911.sqlite
✅ Contains 3 projects with optimization stages for EURUSD, GBPUSD, EURGBP

## Python Integration
✅ Python script: {MT5_TERMINAL_PATH}\\Experts\\ClusteringStage1.py
✅ Will be called automatically during clustering stage

## Troubleshooting
- If compilation errors occur, check that include paths use "MultiCurrency/" prefix
- Ensure all .mqh files are in Include\\MultiCurrency\\ folder
- Verify database file is in Files\\ folder
"""
    
    instructions_file = Path("COMPILATION_INSTRUCTIONS.txt")
    with open(instructions_file, 'w', encoding='utf-8') as f:
        f.write(instructions)
    
    print(f"📖 Created compilation instructions: {instructions_file}")
    return True

def main():
    """Main function."""
    
    print("🔧 MT5 Terminal Include Path Fixer")
    print("=" * 50)
    
    # Fix include paths
    if fix_include_paths_in_terminal():
        print("\n✅ Include paths fixed in terminal files!")
    else:
        print("\n❌ Failed to fix include paths!")
        return False
    
    # Create instructions
    create_compilation_instructions()
    
    print("\n🎉 Terminal files are ready for compilation!")
    print("\nNext steps:")
    print("1. Open MetaEditor (F4 in MT5)")
    print("2. Compile Optimization.mq5 first")
    print("3. Compile other Expert Advisors")
    print("4. Load Optimization.ex5 on a chart")
    
    return True

if __name__ == "__main__":
    main()
    input("\nPress Enter to continue...")
