#!/usr/bin/env python3
"""
Quick script to check Task 4 optimization results
"""
import sqlite3
import os

def check_results():
    db_path = "Database/database911.sqlite"
    
    if not os.path.exists(db_path):
        print(f"❌ Database file {db_path} not found!")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # First, let's see what tables exist
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"📋 Available tables: {[t[0] for t in tables]}")

        # Check tasks table structure first
        cursor.execute("PRAGMA table_info(tasks)")
        task_columns = cursor.fetchall()
        print(f"📋 Tasks table columns: {[col[1] for col in task_columns]}")

        # Check all tasks
        cursor.execute("SELECT * FROM tasks")
        all_tasks = cursor.fetchall()
        print(f"📋 All tasks: {all_tasks}")

        # Check if Task 4 exists (assuming first column is task ID)
        task_4_found = False
        for task in all_tasks:
            if task[0] == 4:  # Assuming first column is ID
                print(f"✅ Task 4 found: {task}")
                task_4_found = True
                break

        if not task_4_found:
            print("❌ Task 4 not found in tasks table")

        # Try to find results in any table that might contain them
        for table_name in [t[0] for t in tables]:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                print(f"📊 Table '{table_name}' has {count} rows")

                # If it has data, show a sample
                if count > 0:
                    cursor.execute(f"SELECT * FROM {table_name} LIMIT 3")
                    sample = cursor.fetchall()
                    print(f"   Sample data: {sample}")
            except Exception as e:
                print(f"❌ Error checking table {table_name}: {e}")

        conn.close()
        
    except Exception as e:
        print(f"❌ Error checking results: {e}")

if __name__ == "__main__":
    check_results()
