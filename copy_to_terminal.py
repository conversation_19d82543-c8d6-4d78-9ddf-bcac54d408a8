#!/usr/bin/env python3
"""
Copy Essential Files to MT5 Terminal
This script copies only the necessary files to your MT5 terminal directory
while keeping your development files in the current location.
"""

import os
import shutil
from pathlib import Path

# Your MT5 Terminal Path
MT5_TERMINAL_PATH = r"C:\Users\<USER>\AppData\Roaming\MetaQuotes\Terminal\7BC3F33EDFDBDBDBADB45838B9A2D03F\MQL5"

def create_terminal_directories():
    """Create necessary directories in MT5 terminal."""
    
    print("📁 Creating directories in MT5 terminal...")
    
    directories = [
        "Experts",
        "Include/MultiCurrency", 
        "Files"
    ]
    
    for directory in directories:
        dir_path = Path(MT5_TERMINAL_PATH) / directory
        dir_path.mkdir(parents=True, exist_ok=True)
        print(f"  ✓ {directory}")
    
    return True

def copy_source_files():
    """Copy source files (.mq5 and .mqh) for compilation."""
    
    print("\n📋 Copying source files for compilation...")
    
    # Expert Advisor source files
    expert_files = [
        "Optimization.mq5",
        "SimpleVolumesExpert.mq5", 
        "SimpleVolumesStage1.mq5",
        "SimpleVolumesStage2.mq5",
        "SimpleVolumesStage3.mq5",
        "HistoryReceiverExpert.mq5",
        "SimpleHistoryReceiverExpert.mq5",
        "LibraryExport.mq5"
    ]
    
    # Include files
    include_files = [
        "Advisor.mqh",
        "Database.mqh",
        "GroupsLibrary.mqh",
        "Money.mqh", 
        "Optimizer.mqh",
        "OptimizerTask.mqh",
        "Strategy.mqh",
        "TesterHandler.mqh",
        "VirtualAdvisor.mqh",
        "VirtualInterface.mqh",
        "VirtualOrder.mqh",
        "VirtualReceiver.mqh",
        "VirtualRiskManager.mqh",
        "VirtualStrategy.mqh",
        "SimpleVolumesStrategy.mqh",
        "HistoryStrategy.mqh",
        "VirtualChartOrder.mqh",
        "VirtualFactory.mqh",
        "VirtualHistoryAdvisor.mqh",
        "VirtualStrategyGroup.mqh",
        "VirtualSymbolReceiver.mqh",
        "ExpertHistory.mqh",
        "ExportedGroupsLibrary.mqh",
        "Factorable.mqh",
        "Interface.mqh",
        "Macros.mqh",
        "NewBarEvent.mqh",
        "Receiver.mqh"
    ]
    
    # Copy Expert Advisor files
    experts_dest = Path(MT5_TERMINAL_PATH) / "Experts"
    for file in expert_files:
        source = Path(file)
        if source.exists():
            dest = experts_dest / file
            shutil.copy2(source, dest)
            print(f"  ✓ {file} → Experts/")
        else:
            print(f"  ⚠ Missing: {file}")
    
    # Copy Include files
    include_dest = Path(MT5_TERMINAL_PATH) / "Include" / "MultiCurrency"
    for file in include_files:
        source = Path(file)
        if source.exists():
            dest = include_dest / file
            shutil.copy2(source, dest)
            print(f"  ✓ {file} → Include/MultiCurrency/")
        else:
            print(f"  ⚠ Missing: {file}")
    
    return True

def copy_essential_files():
    """Copy essential runtime files."""
    
    print("\n🗄️ Copying essential runtime files...")
    
    # Database file
    db_source = Path("Database/database911.sqlite")
    if db_source.exists():
        db_dest = Path(MT5_TERMINAL_PATH) / "Files" / "database911.sqlite"
        shutil.copy2(db_source, db_dest)
        print(f"  ✓ database911.sqlite → Files/")
    else:
        print(f"  ❌ Database file not found: {db_source}")
        return False
    
    # Python clustering script
    python_source = Path("Python/ClusteringStage1.py")
    if python_source.exists():
        python_dest = Path(MT5_TERMINAL_PATH) / "Experts" / "ClusteringStage1.py"
        shutil.copy2(python_source, python_dest)
        print(f"  ✓ ClusteringStage1.py → Experts/")
    else:
        print(f"  ❌ Python script not found: {python_source}")
        return False
    
    return True

def copy_compiled_files():
    """Copy compiled .ex5 files if they exist."""
    
    print("\n⚙️ Looking for compiled files...")
    
    # Look for .ex5 files in current directory and MQL5/Experts
    search_paths = [Path("."), Path("MQL5/Experts")]
    
    ex5_files = []
    for search_path in search_paths:
        if search_path.exists():
            ex5_files.extend(search_path.glob("*.ex5"))
    
    if ex5_files:
        experts_dest = Path(MT5_TERMINAL_PATH) / "Experts"
        for ex5_file in ex5_files:
            dest = experts_dest / ex5_file.name
            shutil.copy2(ex5_file, dest)
            print(f"  ✓ {ex5_file.name} → Experts/")
    else:
        print("  ℹ️ No compiled .ex5 files found (compile after copying source files)")
    
    return True

def verify_copy():
    """Verify that essential files were copied correctly."""
    
    print("\n🔍 Verifying copied files...")
    
    # Check essential files
    essential_files = [
        ("Files/database911.sqlite", "Database file"),
        ("Experts/ClusteringStage1.py", "Python clustering script"),
        ("Experts/Optimization.mq5", "Main optimization EA source"),
        ("Include/MultiCurrency/Optimizer.mqh", "Optimizer include file")
    ]
    
    all_good = True
    for file_path, description in essential_files:
        full_path = Path(MT5_TERMINAL_PATH) / file_path
        if full_path.exists():
            print(f"  ✓ {description}")
        else:
            print(f"  ❌ Missing: {description}")
            all_good = False
    
    return all_good

def main():
    """Main copy function."""
    
    print("📦 Multi-Currency FX EA - Copy to MT5 Terminal")
    print("=" * 60)
    print(f"Terminal Path: {MT5_TERMINAL_PATH}")
    print()
    
    # Check if terminal path exists
    if not Path(MT5_TERMINAL_PATH).exists():
        print(f"❌ MT5 Terminal path not found: {MT5_TERMINAL_PATH}")
        print("Please verify your MT5 terminal path and update the script.")
        return False
    
    # Execute copy steps
    steps = [
        ("Creating terminal directories", create_terminal_directories),
        ("Copying source files", copy_source_files),
        ("Copying essential runtime files", copy_essential_files),
        ("Copying compiled files", copy_compiled_files),
        ("Verifying copy", verify_copy)
    ]
    
    for step_name, step_function in steps:
        print(f"🔄 {step_name}...")
        if not step_function():
            print(f"❌ Failed: {step_name}")
            return False
    
    print("\n" + "=" * 60)
    print("🎉 Files copied successfully to MT5 terminal!")
    print("\nNext steps:")
    print("1. Open MetaEditor (F4 in MT5)")
    print("2. Navigate to Experts folder in MetaEditor")
    print("3. Compile Optimization.mq5 first")
    print("4. Compile other Expert Advisors")
    print("5. Load Optimization.ex5 on any chart")
    print("\nNote: Source files remain in your development directory for editing.")
    
    return True

if __name__ == "__main__":
    if main():
        input("\nPress Enter to continue...")
    else:
        input("\nPress Enter to exit...")
