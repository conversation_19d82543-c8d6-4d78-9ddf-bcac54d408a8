# Compilation Instructions for MT5 Terminal

## Files Location
All files have been copied to: C:\Users\<USER>\AppData\Roaming\MetaQuotes\Terminal\7BC3F33EDFDBDBDBADB45838B9A2D03F\MQL5

## Compilation Steps

### 1. Open MetaEditor
- In MetaTrader 5, press F4 or go to Tools → MetaQuotes Language Editor

### 2. Navigate to Files
- In MetaEditor Navigator, expand "Experts" folder
- You should see all the .mq5 files

### 3. Compilation Order
1. **First**: Compile Optimization.mq5 ⭐ (Most important)
2. **Then**: Compile other Expert Advisors:
   - SimpleVolumesExpert.mq5
   - SimpleVolumesStage1.mq5
   - SimpleVolumesStage2.mq5
   - SimpleVolumesStage3.mq5
   - HistoryReceiverExpert.mq5
   - SimpleHistoryReceiverExpert.mq5
   - LibraryExport.mq5

### 4. Check for Compilation Success
- Look for "0 error(s), 0 warning(s)" in the compilation log
- .ex5 files should appear in the Experts folder

### 5. Load the System
1. In MT5, open any chart (EURUSD recommended)
2. Drag Optimization.ex5 onto the chart
3. In EA settings:
   - Allow DLL imports: ✅
   - Allow WebRequest: ✅
   - Allow external experts: ✅

### 6. Verify System Start
- Check Expert tab for "Database connected successfully"
- Look for "Starting optimization task..." messages

## Database Status
✅ Database file: C:\Users\<USER>\AppData\Roaming\MetaQuotes\Terminal\7BC3F33EDFDBDBDBADB45838B9A2D03F\MQL5\Files\database911.sqlite
✅ Contains 3 projects with optimization stages for EURUSD, GBPUSD, EURGBP

## Python Integration
✅ Python script: C:\Users\<USER>\AppData\Roaming\MetaQuotes\Terminal\7BC3F33EDFDBDBDBADB45838B9A2D03F\MQL5\Experts\ClusteringStage1.py
✅ Will be called automatically during clustering stage

## Troubleshooting
- If compilation errors occur, check that include paths use "MultiCurrency/" prefix
- Ensure all .mqh files are in Include\MultiCurrency\ folder
- Verify database file is in Files\ folder
